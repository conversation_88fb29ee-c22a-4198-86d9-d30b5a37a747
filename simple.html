<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版分子生物学可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        header {
            background-color: #2c3e50;
            color: white;
            text-align: center;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            width: 100%;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .canvas-container {
            width: 100%;
            height: 500px;
            background-color: #000;
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }
        
        canvas {
            width: 100%;
            height: 100%;
        }
        
        .molecule-info {
            width: 100%;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <header>
        <h1>简化版分子生物学可视化</h1>
    </header>
    
    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label for="molecule-select">选择分子:</label>
                <select id="molecule-select">
                    <option value="water">水分子 (H₂O)</option>
                    <option value="oxygen">氧气分子 (O₂)</option>
                    <option value="carbon-dioxide">二氧化碳 (CO₂)</option>
                    <option value="methane">甲烷 (CH₄)</option>
                </select>
            </div>
            
            <div class="control-group">
                <button id="rotate-button">旋转分子</button>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="molecule-canvas"></canvas>
        </div>
        
        <div class="molecule-info">
            <h2 id="molecule-name">水分子 (H₂O)</h2>
            <p id="molecule-description">水分子由一个氧原子和两个氢原子组成，是生命存在的基础。</p>
        </div>
    </div>
    
    <script>
        // 分子数据
        const MOLECULES = {
            'water': {
                name: '水分子 (H₂O)',
                description: '水分子由一个氧原子和两个氢原子组成，是生命存在的基础。',
                atoms: [
                    { symbol: 'O', x: 0, y: 0, radius: 30, color: 'red' },
                    { symbol: 'H', x: -40, y: -30, radius: 15, color: 'white' },
                    { symbol: 'H', x: 40, y: -30, radius: 15, color: 'white' }
                ],
                bonds: [
                    { from: 0, to: 1 },
                    { from: 0, to: 2 }
                ]
            },
            'oxygen': {
                name: '氧气分子 (O₂)',
                description: '氧气分子由两个氧原子组成，是呼吸所必需的。',
                atoms: [
                    { symbol: 'O', x: -25, y: 0, radius: 30, color: 'red' },
                    { symbol: 'O', x: 25, y: 0, radius: 30, color: 'red' }
                ],
                bonds: [
                    { from: 0, to: 1 }
                ]
            },
            'carbon-dioxide': {
                name: '二氧化碳 (CO₂)',
                description: '二氧化碳由一个碳原子和两个氧原子组成，是光合作用的原料。',
                atoms: [
                    { symbol: 'C', x: 0, y: 0, radius: 25, color: 'gray' },
                    { symbol: 'O', x: -50, y: 0, radius: 30, color: 'red' },
                    { symbol: 'O', x: 50, y: 0, radius: 30, color: 'red' }
                ],
                bonds: [
                    { from: 0, to: 1 },
                    { from: 0, to: 2 }
                ]
            },
            'methane': {
                name: '甲烷 (CH₄)',
                description: '甲烷由一个碳原子和四个氢原子组成，是最简单的烷烃。',
                atoms: [
                    { symbol: 'C', x: 0, y: 0, radius: 25, color: 'gray' },
                    { symbol: 'H', x: -30, y: -30, radius: 15, color: 'white' },
                    { symbol: 'H', x: 30, y: -30, radius: 15, color: 'white' },
                    { symbol: 'H', x: -30, y: 30, radius: 15, color: 'white' },
                    { symbol: 'H', x: 30, y: 30, radius: 15, color: 'white' }
                ],
                bonds: [
                    { from: 0, to: 1 },
                    { from: 0, to: 2 },
                    { from: 0, to: 3 },
                    { from: 0, to: 4 }
                ]
            }
        };
        
        // 获取Canvas元素和上下文
        const canvas = document.getElementById('molecule-canvas');
        const ctx = canvas.getContext('2d');
        
        // 设置Canvas大小
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }
        
        // 初始化时调整Canvas大小
        resizeCanvas();
        
        // 窗口大小变化时调整Canvas大小
        window.addEventListener('resize', resizeCanvas);
        
        // 当前选中的分子
        let currentMolecule = MOLECULES['water'];
        
        // 旋转角度
        let rotationAngle = 0;
        let isRotating = false;
        
        // 绘制分子
        function drawMolecule() {
            // 清除Canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 计算分子中心
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制键
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 5;
            
            currentMolecule.bonds.forEach(bond => {
                const fromAtom = currentMolecule.atoms[bond.from];
                const toAtom = currentMolecule.atoms[bond.to];
                
                // 应用旋转
                const fromX = fromAtom.x * Math.cos(rotationAngle) - fromAtom.y * Math.sin(rotationAngle);
                const fromY = fromAtom.x * Math.sin(rotationAngle) + fromAtom.y * Math.cos(rotationAngle);
                
                const toX = toAtom.x * Math.cos(rotationAngle) - toAtom.y * Math.sin(rotationAngle);
                const toY = toAtom.x * Math.sin(rotationAngle) + toAtom.y * Math.cos(rotationAngle);
                
                ctx.beginPath();
                ctx.moveTo(centerX + fromX, centerY + fromY);
                ctx.lineTo(centerX + toX, centerY + toY);
                ctx.stroke();
            });
            
            // 绘制原子
            currentMolecule.atoms.forEach(atom => {
                // 应用旋转
                const x = atom.x * Math.cos(rotationAngle) - atom.y * Math.sin(rotationAngle);
                const y = atom.x * Math.sin(rotationAngle) + atom.y * Math.cos(rotationAngle);
                
                // 绘制原子
                ctx.beginPath();
                ctx.arc(centerX + x, centerY + y, atom.radius, 0, Math.PI * 2);
                ctx.fillStyle = atom.color;
                ctx.fill();
                
                // 绘制原子符号
                ctx.fillStyle = (atom.color === 'white') ? 'black' : 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(atom.symbol, centerX + x, centerY + y);
            });
        }
        
        // 动画循环
        function animate() {
            if (isRotating) {
                rotationAngle += 0.01;
            }
            
            drawMolecule();
            requestAnimationFrame(animate);
        }
        
        // 开始动画
        animate();
        
        // 处理分子选择变化
        document.getElementById('molecule-select').addEventListener('change', function() {
            const moleculeId = this.value;
            currentMolecule = MOLECULES[moleculeId];
            
            // 更新分子信息
            document.getElementById('molecule-name').textContent = currentMolecule.name;
            document.getElementById('molecule-description').textContent = currentMolecule.description;
        });
        
        // 处理旋转按钮点击
        document.getElementById('rotate-button').addEventListener('click', function() {
            isRotating = !isRotating;
            this.textContent = isRotating ? '停止旋转' : '旋转分子';
        });
    </script>
</body>
</html>
