<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>基础分子可视化</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
        }
        
        h1 {
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        select, button {
            padding: 8px;
            margin: 10px;
        }
        
        #molecule-display {
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        
        .atom {
            display: inline-block;
            width: 50px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            border-radius: 50%;
            margin: 5px;
            font-weight: bold;
        }
        
        .bond {
            display: inline-block;
            width: 30px;
            height: 5px;
            background-color: #333;
            margin: 25px 0;
        }
    </style>
</head>
<body>
    <h1>基础分子可视化</h1>
    
    <div class="container">
        <div>
            <label for="molecule-select">选择分子: </label>
            <select id="molecule-select">
                <option value="water">水分子 (H₂O)</option>
                <option value="oxygen">氧气 (O₂)</option>
                <option value="carbon-dioxide">二氧化碳 (CO₂)</option>
                <option value="methane">甲烷 (CH₄)</option>
            </select>
            
            <button id="animate-button">动画效果</button>
        </div>
        
        <div id="molecule-display">
            <!-- 分子结构将在这里显示 -->
        </div>
        
        <div id="molecule-info">
            <h3 id="molecule-name">水分子 (H₂O)</h3>
            <p id="molecule-description">水分子由一个氧原子和两个氢原子组成，是生命存在的基础。</p>
        </div>
    </div>
    
    <script>
        // 分子数据
        const molecules = {
            water: {
                name: '水分子 (H₂O)',
                description: '水分子由一个氧原子和两个氢原子组成，是生命存在的基础。',
                structure: [
                    { symbol: 'H', color: '#FFFFFF', background: '#0000FF' },
                    { symbol: 'O', color: '#FFFFFF', background: '#FF0000' },
                    { symbol: 'H', color: '#FFFFFF', background: '#0000FF' }
                ],
                bonds: [0, 1, 1, 2]
            },
            oxygen: {
                name: '氧气 (O₂)',
                description: '氧气分子由两个氧原子组成，是呼吸所必需的。',
                structure: [
                    { symbol: 'O', color: '#FFFFFF', background: '#FF0000' },
                    { symbol: 'O', color: '#FFFFFF', background: '#FF0000' }
                ],
                bonds: [0, 1]
            },
            'carbon-dioxide': {
                name: '二氧化碳 (CO₂)',
                description: '二氧化碳由一个碳原子和两个氧原子组成，是光合作用的原料。',
                structure: [
                    { symbol: 'O', color: '#FFFFFF', background: '#FF0000' },
                    { symbol: 'C', color: '#FFFFFF', background: '#333333' },
                    { symbol: 'O', color: '#FFFFFF', background: '#FF0000' }
                ],
                bonds: [0, 1, 1, 2]
            },
            methane: {
                name: '甲烷 (CH₄)',
                description: '甲烷由一个碳原子和四个氢原子组成，是最简单的烷烃。',
                structure: [
                    { symbol: 'H', color: '#FFFFFF', background: '#0000FF' },
                    { symbol: 'H', color: '#FFFFFF', background: '#0000FF' },
                    { symbol: 'C', color: '#FFFFFF', background: '#333333' },
                    { symbol: 'H', color: '#FFFFFF', background: '#0000FF' },
                    { symbol: 'H', color: '#FFFFFF', background: '#0000FF' }
                ],
                bonds: [0, 2, 1, 2, 2, 3, 2, 4]
            }
        };
        
        // 获取DOM元素
        const moleculeSelect = document.getElementById('molecule-select');
        const animateButton = document.getElementById('animate-button');
        const moleculeDisplay = document.getElementById('molecule-display');
        const moleculeName = document.getElementById('molecule-name');
        const moleculeDescription = document.getElementById('molecule-description');
        
        // 动画状态
        let isAnimating = false;
        let animationInterval;
        
        // 显示分子
        function displayMolecule(moleculeId) {
            const molecule = molecules[moleculeId];
            
            // 更新分子信息
            moleculeName.textContent = molecule.name;
            moleculeDescription.textContent = molecule.description;
            
            // 清空分子显示区域
            moleculeDisplay.innerHTML = '';
            
            // 创建分子结构
            const structure = document.createElement('div');
            
            // 添加原子和键
            for (let i = 0; i < molecule.structure.length; i++) {
                const atom = molecule.structure[i];
                
                // 如果需要添加键
                if (i > 0) {
                    // 查找是否有连接到这个原子的键
                    for (let j = 0; j < molecule.bonds.length; j += 2) {
                        if (molecule.bonds[j+1] === i) {
                            const bond = document.createElement('div');
                            bond.className = 'bond';
                            structure.appendChild(bond);
                            break;
                        }
                    }
                }
                
                // 添加原子
                const atomElement = document.createElement('div');
                atomElement.className = 'atom';
                atomElement.textContent = atom.symbol;
                atomElement.style.color = atom.color;
                atomElement.style.backgroundColor = atom.background;
                structure.appendChild(atomElement);
            }
            
            moleculeDisplay.appendChild(structure);
        }
        
        // 动画效果
        function toggleAnimation() {
            if (isAnimating) {
                clearInterval(animationInterval);
                animateButton.textContent = '动画效果';
                isAnimating = false;
            } else {
                let scale = 1;
                let growing = false;
                
                animationInterval = setInterval(() => {
                    if (growing) {
                        scale += 0.05;
                        if (scale >= 1.2) {
                            growing = false;
                        }
                    } else {
                        scale -= 0.05;
                        if (scale <= 0.8) {
                            growing = true;
                        }
                    }
                    
                    const atoms = document.querySelectorAll('.atom');
                    atoms.forEach(atom => {
                        atom.style.transform = `scale(${scale})`;
                    });
                }, 100);
                
                animateButton.textContent = '停止动画';
                isAnimating = true;
            }
        }
        
        // 事件监听器
        moleculeSelect.addEventListener('change', () => {
            displayMolecule(moleculeSelect.value);
        });
        
        animateButton.addEventListener('click', toggleAnimation);
        
        // 初始显示
        displayMolecule('water');
    </script>
</body>
</html>
