<!DOCTYPE html>
<html>
<head>
    <title>MOLECULES测试</title>
</head>
<body>
    <h1>MOLECULES对象测试</h1>
    <div id="result"></div>

    <script src="data/molecules_new.js"></script>
    <script>
        window.onload = function() {
            const resultDiv = document.getElementById('result');
            
            if (typeof MOLECULES !== 'undefined') {
                resultDiv.innerHTML = `
                    <p style="color: green;">MOLECULES对象已成功加载!</p>
                    <p>可用分子: ${Object.keys(MOLECULES).join(', ')}</p>
                    <p>第一个分子: ${MOLECULES[Object.keys(MOLECULES)[0]].name}</p>
                `;
            } else {
                resultDiv.innerHTML = `
                    <p style="color: red;">错误: MOLECULES对象未定义!</p>
                `;
            }
        };
    </script>
</body>
</html>
