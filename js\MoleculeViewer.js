/**
 * 分子可视化器类
 * 负责使用Three.js渲染分子结构
 * 增强版 - 包含更多渲染效果和动画类型
 */
class MoleculeViewer {
    /**
     * 构造函数
     * @param {HTMLElement} container - 容器元素
     */
    constructor(container) {
        // 全局动画参数
        this.mdAmplitude = 0.15; // 默认振幅值
        // 检查THREE是否已定义
        if (typeof THREE === 'undefined') {
            console.error('THREE未定义，无法初始化MoleculeViewer');
            this._showErrorInContainer(container, 'THREE.js库未加载，请检查网络连接或刷新页面');
            this.renderingFailed = true;
            return;
        }

        // 检查WebGL支持
        if (!this._checkWebGLSupport()) {
            console.warn('WebGL不受支持，将使用备用渲染器');
            this._showErrorInContainer(container, 'WebGL不受支持，使用备用渲染模式');
            this.renderingFailed = true;
            return;
        }

        this.container = container;
        this.currentMolecule = null;
        this.displayMode = 'ball-stick';
        this.animationSpeed = 0.5;
        this.isPlaying = true;
        this.atomMeshes = [];
        this.bondMeshes = [];
        this.animationMixers = [];
        this.particles = []; // 用于粒子效果
        this.useHighQuality = true; // 高质量渲染开关
        this.usePostProcessing = false; // 默认关闭后期处理，避免兼容性问题
        this.renderingFailed = false; // 渲染失败标志

        try {
            // 创建时钟对象
            try {
                this.clock = new THREE.Clock();
            } catch (e) {
                console.warn('无法创建THREE.Clock，使用备用时钟');
                this.clock = {
                    getDelta: function() { return 0.016; }, // 约60fps
                    elapsedTime: 0,
                    _lastTime: Date.now(),
                    _update: function() {
                        const now = Date.now();
                        const delta = (now - this._lastTime) / 1000;
                        this.elapsedTime += delta;
                        this._lastTime = now;
                        return delta;
                    }
                };
            }

            // 初始化备用渲染器
            if (typeof FallbackRenderer !== 'undefined') {
                this.fallbackRenderer = new FallbackRenderer(container);
                console.log('备用渲染器已初始化');
            }

            // 初始化Three.js组件
            this._initScene();
            this._initCamera();
            this._initRenderer();
            this._initLights();
            this._initControls();

            // 仅在高级浏览器中启用后期处理
            if (this._isModernBrowser()) {
                this._initPostProcessing();
            }

            // 开始渲染循环
            this._animate();

            // 处理窗口大小变化
            window.addEventListener('resize', () => this._onWindowResize());

            console.log('MoleculeViewer初始化成功');
        } catch (error) {
            console.error('MoleculeViewer初始化失败:', error);
            this.renderingFailed = true;

            // 激活备用渲染器
            if (this.fallbackRenderer) {
                this.fallbackRenderer.activate('3D渲染初始化失败，已切换到2D模式');
            } else {
                this._showErrorInContainer(container, 'MoleculeViewer未定义，请检查相关脚本是否加载');
            }
        }
    }

    /**
     * 在容器中显示错误信息
     * @private
     * @param {HTMLElement} container - 容器元素
     * @param {string} message - 错误信息
     */
    _showErrorInContainer(container, message) {
        if (!container) return;

        container.innerHTML = `
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                        display: flex; flex-direction: column; align-items: center; justify-content: center;
                        background-color: rgba(0,0,0,0.8); color: white; text-align: center; padding: 20px;">
                <h3 style="color: #ff5555; margin-bottom: 15px;">加载错误</h3>
                <p style="margin-bottom: 20px;">${message}</p>
                <ul style="text-align: left; margin-bottom: 20px;">
                    <li>检查您的网络连接是否正常</li>
                    <li>尝试使用Chrome或Firefox浏览器</li>
                    <li>确保您的浏览器已启用JavaScript和WebGL</li>
                    <li>清除浏览器缓存后重试</li>
                </ul>
                <div>
                    <button onclick="window.location.reload()"
                            style="padding: 10px 20px; background-color: #3498db; border: none;
                                   border-radius: 5px; color: white; cursor: pointer; margin: 5px;">
                        刷新页面
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 检查是否为现代浏览器
     * @private
     * @returns {boolean} 是否为现代浏览器
     */
    _isModernBrowser() {
        // 检查WebGL2支持
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl2');
            if (gl) {
                return true;
            }
        } catch (e) {
            console.log('WebGL2不受支持');
        }

        // 检查浏览器版本
        const ua = navigator.userAgent;

        // Chrome 70+, Firefox 65+, Safari 12+, Edge 79+
        if ((/Chrome\/([0-9]+)/.exec(ua) && parseInt(RegExp.$1) >= 70) ||
            (/Firefox\/([0-9]+)/.exec(ua) && parseInt(RegExp.$1) >= 65) ||
            (/Safari\/([0-9]+)/.exec(ua) && parseInt(RegExp.$1) >= 12 && !/Chrome/.test(ua)) ||
            (/Edg\/([0-9]+)/.exec(ua) && parseInt(RegExp.$1) >= 79)) {
            return true;
        }

        return false;
    }

    /**
     * 初始化Three.js场景
     * @private
     */
    _initScene() {
        this.scene = new THREE.Scene();

        // 设置场景背景为纯黑色
        this.scene.background = new THREE.Color(0x000000);

        // 移除环境雾效果，确保背景纯黑
        // this.scene.fog = new THREE.FogExp2(0x000000, 0.005);

        // 添加坐标轴辅助工具 (调试用)
        // const axesHelper = new THREE.AxesHelper(5);
        // this.scene.add(axesHelper);
    }

    /**
     * 初始化相机
     * @private
     */
    _initCamera() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        this.camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000);

        // 设置相机位置，使其能够更好地观察分子结构
        this.camera.position.set(20, 15, 30);
        this.camera.lookAt(0, 0, 0);
    }

    /**
     * 初始化渲染器
     * @private
     */
    _initRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio || 1);

        // 简化版Three.js可能不支持这些高级功能，使用try-catch包装
        try {
            this.renderer.shadowMap.enabled = true;
            if (THREE.PCFSoftShadowMap) {
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            }
        } catch (e) {
            console.warn('阴影映射功能不可用:', e);
        }

        try {
            if (THREE.sRGBEncoding) {
                this.renderer.outputEncoding = THREE.sRGBEncoding;
            }
            if (THREE.ACESFilmicToneMapping) {
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.0;
            }
            if (typeof this.renderer.physicallyCorrectLights !== 'undefined') {
                this.renderer.physicallyCorrectLights = true;
            }
        } catch (e) {
            console.warn('高级渲染功能不可用:', e);
        }
        this.container.appendChild(this.renderer.domElement);
    }

    /**
     * 初始化光照
     * @private
     */
    _initLights() {
        // 环境光 - 提高亮度和颜色
        const ambientLight = new THREE.AmbientLight(0x606060, 0.6);
        this.scene.add(ambientLight);

        // 主方向光 - 添加阴影
        const mainLight = new THREE.DirectionalLight(0xffffff, 1.0);
        mainLight.position.set(5, 10, 7.5);
        mainLight.castShadow = true;

        // 配置阴影
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 50;
        mainLight.shadow.bias = -0.0001;

        const d = 30;
        mainLight.shadow.camera.left = -d;
        mainLight.shadow.camera.right = d;
        mainLight.shadow.camera.top = d;
        mainLight.shadow.camera.bottom = -d;

        this.scene.add(mainLight);

        // 辅助方向光 - 从另一侧照亮
        const fillLight = new THREE.DirectionalLight(0x8080ff, 0.4); // 蓝色调光源
        fillLight.position.set(-5, -2, -7.5);
        this.scene.add(fillLight);

        // 点光源 - 增加高光
        const pointLight1 = new THREE.PointLight(0xffffff, 0.8);
        pointLight1.position.set(-10, 10, 10);
        this.scene.add(pointLight1);

        // 第二个点光源 - 不同颜色
        const pointLight2 = new THREE.PointLight(0xffaa00, 0.5); // 暖色调
        pointLight2.position.set(10, -5, -10);
        this.scene.add(pointLight2);

        // 添加半球光 - 提供更自然的环境光照
        const hemiLight = new THREE.HemisphereLight(0xffffbb, 0x080820, 0.3);
        this.scene.add(hemiLight);
    }

    /**
     * 初始化控制器
     * @private
     */
    _initControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.25;
        this.controls.rotateSpeed = 0.5;
        this.controls.enableZoom = true;
        this.controls.autoRotate = false;
        this.controls.minDistance = 5;
        this.controls.maxDistance = 100;
    }

    /**
     * 初始化后期处理效果
     * @private
     */
    _initPostProcessing() {
        // 默认禁用后期处理
        this.usePostProcessing = false;

        // 在简化版Three.js中，我们不使用后期处理效果
        console.log('使用简化渲染模式，不启用后期处理效果');
        return;
    }

    /**
     * 处理窗口大小变化
     * @private
     */
    _onWindowResize() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);

        // 更新后期处理效果的大小
        if (this.usePostProcessing && this.composer) {
            this.composer.setSize(width, height);

            // 更新FXAA抗锯齿的分辨率
            const fxaaPass = this.composer.passes.find(pass => pass.material && pass.material.uniforms && pass.material.uniforms.resolution);
            if (fxaaPass) {
                fxaaPass.material.uniforms.resolution.value.set(1 / width, 1 / height);
            }
        }
    }

    /**
     * 动画循环
     * @private
     */
    _animate() {
        requestAnimationFrame(() => this._animate());

        try {
            // 获取时间增量
            let delta = 0.016; // 默认值约为60fps

            try {
                if (this.clock && typeof this.clock.getDelta === 'function') {
                    delta = this.clock.getDelta();
                }
            } catch (e) {
                console.warn('时钟对象不可用，使用默认时间增量');
            }

            // 更新控制器
            if (this.controls && typeof this.controls.update === 'function') {
                this.controls.update();
            }

            // 更新动画
            if (this.isPlaying && this.currentMolecule) {
                try {
                    this._updateAnimation(delta);
                } catch (e) {
                    console.warn('动画更新失败:', e);
                }
            }

            // 更新粒子效果 - 简化版可能不支持
            try {
                this._updateParticles(delta);
            } catch (e) {
                // 忽略粒子更新错误
            }

            // 标准渲染 - 简化版本
            if (this.scene && this.camera && this.renderer) {
                this.renderer.render(this.scene, this.camera);
            }
        } catch (error) {
            console.error('动画循环中发生错误:', error);

            // 尝试最基本的渲染
            try {
                if (this.scene && this.camera && this.renderer) {
                    this.renderer.render(this.scene, this.camera);
                }
            } catch (e) {
                console.error('基本渲染也失败:', e);
            }
        }
    }

    /**
     * 创建能量粒子效果
     * @private
     * @param {THREE.Vector3} position - 粒子生成位置
     */
    _createEnergyParticle(position) {
        // 在简化版中禁用粒子效果
        return;
    }

    /**
     * 更新粒子效果
     * @private
     * @param {number} delta - 时间增量
     */
    _updateParticles(delta) {
        // 在简化版中禁用粒子效果
        return;
    }

    /**
     * 平滑过渡函数
     * @private
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {number} value - 当前值
     * @returns {number} 平滑过渡后的值
     */
    _smoothStep(min, max, value) {
        const x = Math.max(0, Math.min(1, (value - min) / (max - min)));
        return x * x * (3 - 2 * x);
    }

    /**
     * 更新分子动画
     * @private
     * @param {number} delta - 时间增量
     */
    _updateAnimation(delta) {
        try {
            const molecule = this.currentMolecule;
            if (!molecule) return;

            // 如果没有动画设置，使用默认的振动动画
            const animation = molecule.animation || { type: 'vibration', speed: 1.0, amplitude: 0.1 };
            const speed = this.animationSpeed * (animation.speed || 0.01);

        switch (animation.type) {
            case 'rotation':
                // 增强的整体旋转 - 添加轻微的摆动效果
                const rotationTime = this.clock.elapsedTime;
                const wobbleAmount = 0.05; // 摆动幅度
                const wobbleSpeed = 0.5;   // 摆动速度

                // 基础旋转
                this.moleculeGroup.rotation.x += speed * (animation.axis[0] || 0);
                this.moleculeGroup.rotation.y += speed * (animation.axis[1] || 0);
                this.moleculeGroup.rotation.z += speed * (animation.axis[2] || 0);

                // 添加摆动效果
                this.moleculeGroup.rotation.x += Math.sin(rotationTime * wobbleSpeed) * wobbleAmount * delta;
                this.moleculeGroup.rotation.z += Math.cos(rotationTime * wobbleSpeed * 0.7) * wobbleAmount * delta;

                // 添加轻微的位置浮动
                if (!this.moleculeGroup.userData.originalPosition) {
                    this.moleculeGroup.userData.originalPosition = new THREE.Vector3(0, 0, 0);
                }

                const floatY = Math.sin(rotationTime * 0.8) * 0.2;
                this.moleculeGroup.position.y = this.moleculeGroup.userData.originalPosition.y + floatY;
                break;

            case 'vibration':
                // 分子振动 - 增强版，更自然的振动和视觉效果
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];
                    const amplitude = animation.amplitude || 0.1;
                    const frequency = animation.frequency || 0.05;

                    // 如果没有初始化振动方向和相位，则初始化
                    if (!mesh.userData.vibrationDir) {
                        mesh.userData.vibrationDir = new THREE.Vector3(
                            Math.random() - 0.5,
                            Math.random() - 0.5,
                            Math.random() - 0.5
                        ).normalize();

                        // 为每个原子添加随机相位，使振动不同步
                        mesh.userData.vibrationPhase = Math.random() * Math.PI * 2;

                        // 为每个原子添加随机频率变化，使振动更自然
                        mesh.userData.vibrationFreqMod = 0.8 + Math.random() * 0.4;

                        // 为每个原子添加独特的振动特性
                        mesh.userData.vibrationPattern = Math.floor(Math.random() * 3); // 0, 1, 或 2
                    }

                    const dir = mesh.userData.vibrationDir;
                    const phase = mesh.userData.vibrationPhase;
                    const freqMod = mesh.userData.vibrationFreqMod;
                    const pattern = mesh.userData.vibrationPattern;

                    // 基于时间的振动因子
                    const time = this.clock.elapsedTime;

                    // 根据不同的振动模式计算偏移
                    let offset;

                    switch (pattern) {
                        case 0: // 标准振动
                            // 复合振动 = 主振动 + 小振动
                            const mainOffset = amplitude * Math.sin(time * frequency * Math.PI * 2 * freqMod + phase);
                            const smallOffset = amplitude * 0.3 * Math.sin(time * frequency * Math.PI * 4 * freqMod + phase * 2);
                            offset = mainOffset + smallOffset;
                            break;

                        case 1: // 阻尼振动
                            // 使用指数衰减函数创建阻尼效果
                            const dampingFactor = 0.2 + 0.8 * Math.abs(Math.sin(time * 0.2)); // 0.2-1.0之间变化
                            offset = amplitude * dampingFactor * Math.sin(time * frequency * Math.PI * 2 * freqMod + phase);
                            break;

                        case 2: // 复合振动
                            // 使用多个不同频率的正弦波
                            const wave1 = amplitude * 0.7 * Math.sin(time * frequency * Math.PI * 2 * freqMod + phase);
                            const wave2 = amplitude * 0.2 * Math.sin(time * frequency * Math.PI * 3 * freqMod + phase * 1.5);
                            const wave3 = amplitude * 0.1 * Math.sin(time * frequency * Math.PI * 5 * freqMod + phase * 0.5);
                            offset = wave1 + wave2 + wave3;
                            break;
                    }

                    // 应用振动偏移
                    mesh.position.x = atom.position[0] + dir.x * offset;
                    mesh.position.y = atom.position[1] + dir.y * offset;
                    mesh.position.z = atom.position[2] + dir.z * offset;

                    // 增强的发光效果
                    if (mesh.material && this.useHighQuality) {
                        // 振动越大，发光越强
                        const glowIntensity = 0.2 + Math.abs(offset) * 2;

                        // 根据原子类型设置不同的发光颜色
                        let emissiveColor;
                        switch (atom.symbol) {
                            case 'O': // 氧原子 - 红色发光
                                emissiveColor = new THREE.Color(0.3, 0.1, 0.1);
                                break;
                            case 'N': // 氮原子 - 蓝色发光
                                emissiveColor = new THREE.Color(0.1, 0.1, 0.3);
                                break;
                            case 'C': // 碳原子 - 白色发光
                                emissiveColor = new THREE.Color(0.2, 0.2, 0.2);
                                break;
                            case 'H': // 氢原子 - 淡蓝色发光
                                emissiveColor = new THREE.Color(0.1, 0.2, 0.3);
                                break;
                            default: // 其他原子 - 默认发光
                                emissiveColor = new THREE.Color(
                                    mesh.material.color.r * 0.2,
                                    mesh.material.color.g * 0.2,
                                    mesh.material.color.b * 0.2
                                );
                        }

                        mesh.material.emissive = emissiveColor;
                        mesh.material.emissiveIntensity = glowIntensity;

                        // 随机添加闪烁效果
                        if (Math.random() < 0.01) { // 1%的概率
                            mesh.material.emissiveIntensity = glowIntensity * 2; // 短暂的强烈发光

                            // 如果启用了高质量渲染，添加粒子效果
                            if (this.useHighQuality && Math.random() < 0.3) {
                                this._createEnergyParticle(mesh.position.clone());
                            }

                            // 0.1秒后恢复正常发光
                            setTimeout(() => {
                                if (mesh.material) {
                                    mesh.material.emissiveIntensity = glowIntensity;
                                }
                            }, 100);
                        }
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'phosphateBreak':
                // ATP水解动画 - 增强版，添加粒子效果
                if (animation.bondIndex !== undefined && this.bondMeshes[animation.bondIndex]) {
                    const bond = this.bondMeshes[animation.bondIndex];
                    const progress = (Math.sin(this.clock.elapsedTime * speed * Math.PI) + 1) / 2;

                    // 键断裂动画
                    bond.scale.y = 1 - progress * 0.9;
                    bond.material.opacity = 1 - progress * 0.8;

                    // 磷酸基团移动
                    const phosphateGroup = this.atomMeshes[molecule.bonds[animation.bondIndex].end];
                    if (phosphateGroup) {
                        const originalPos = molecule.atoms[molecule.bonds[animation.bondIndex].end].position;
                        phosphateGroup.position.x = originalPos[0] + progress * 2;
                        phosphateGroup.position.y = originalPos[1] - progress * 1;
                        phosphateGroup.position.z = originalPos[2] + progress * 0.5;

                        // 在断裂点添加能量释放粒子效果
                        if (progress > 0.5 && Math.random() > 0.9 && this.useHighQuality) {
                            this._createEnergyParticle(bond.position);
                        }
                    }
                }
                break;

            case 'folding':
                // 蛋白质折叠动画 - 增强版，更平滑的折叠
                const maxAngle = animation.maxAngle || Math.PI / 8;
                const foldProgress = Math.sin(this.clock.elapsedTime * speed * Math.PI);

                // 改变螺旋的弯曲度，使用平滑的曲线
                if (this.moleculeGroup) {
                    this.moleculeGroup.children.forEach((child, i) => {
                        if (i > 0) {
                            const normalizedIndex = i / this.moleculeGroup.children.length;
                            // 使用平滑的S形曲线
                            const bendFactor = this._smoothStep(0, 1, normalizedIndex) * foldProgress * maxAngle;
                            child.rotation.x = bendFactor;
                            child.rotation.z = bendFactor * 0.5;

                            // 添加轻微的扭曲
                            child.rotation.y = Math.sin(normalizedIndex * Math.PI) * foldProgress * maxAngle * 0.2;
                        }
                    });
                }
                break;

            case 'waveMotion':
                // RNA波动动画
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];
                    const amplitude = animation.amplitude || 0.5;

                    // 根据原子在链中的位置计算波动
                    const waveOffset = amplitude * Math.sin(
                        atom.position[1] * 0.5 + this.clock.elapsedTime * speed * Math.PI * 2
                    );

                    mesh.position.x = atom.position[0] + waveOffset;
                    mesh.position.z = atom.position[2] + waveOffset * 0.5;
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'lipidWave':
                // 磷脂双分子层波动
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];
                    const amplitude = animation.amplitude || 0.3;

                    // 只对尾部碳原子应用波动
                    if (atom.symbol === 'C') {
                        // 根据原子在x-z平面的位置计算波动
                        const xPos = atom.position[0];
                        const zPos = atom.position[2];
                        const waveOffset = amplitude * Math.sin(
                            xPos * 0.5 + zPos * 0.5 + this.clock.elapsedTime * speed * Math.PI * 2
                        );

                        mesh.position.y = atom.position[1] + waveOffset;
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'sheetWave':
                // β折叠波浪动画
                const sheetAmplitude = animation.amplitude || 0.5;
                const time = this.clock.elapsedTime * speed;

                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];

                    // 根据原子在x-z平面的位置计算波动
                    const xPos = atom.position[0];
                    const zPos = atom.position[2];

                    // 创建从一端到另一端的波浪
                    const waveOffset = sheetAmplitude * Math.sin(xPos * 0.2 + time * Math.PI * 2);

                    mesh.position.y = atom.position[1] + waveOffset;
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'catalysis':
                // 酶催化反应动画
                const catalysisProgress = (Math.sin(this.clock.elapsedTime * speed * Math.PI) + 1) / 2;

                // 如果指定了要断裂的键
                if (animation.bondBreakIndex !== undefined && this.bondMeshes[animation.bondBreakIndex]) {
                    const bond = this.bondMeshes[animation.bondBreakIndex];

                    // 键断裂动画
                    if (catalysisProgress > 0.5) {
                        const breakProgress = (catalysisProgress - 0.5) * 2; // 0-1
                        bond.scale.y = 1 - breakProgress * 0.9;
                        bond.material.opacity = 1 - breakProgress * 0.8;

                        // 在断裂点添加反应粒子效果
                        if (Math.random() > 0.9 && this.useHighQuality) {
                            this._createReactionParticle(bond.position);
                        }
                    } else {
                        // 键振动动画
                        const vibrateProgress = catalysisProgress * 2; // 0-1
                        const vibrateOffset = Math.sin(vibrateProgress * Math.PI * 10) * 0.1;
                        bond.scale.y = 1 + vibrateOffset;
                    }
                }

                // 底物分子振动
                const substrateStartIndex = molecule.atoms.length - 6; // 假设底物是最后6个原子
                for (let i = substrateStartIndex; i < molecule.atoms.length; i++) {
                    if (i >= 0 && i < this.atomMeshes.length) {
                        const mesh = this.atomMeshes[i];
                        const atom = molecule.atoms[i];

                        // 振动和旋转
                        const rotateAngle = catalysisProgress * Math.PI * 2;
                        const vibrateOffset = Math.sin(this.clock.elapsedTime * 10) * 0.1 * catalysisProgress;

                        // 计算新位置
                        const originalX = atom.position[0];
                        const originalY = atom.position[1];
                        const originalZ = atom.position[2];

                        // 添加振动和轻微的旋转
                        mesh.position.x = originalX + Math.cos(rotateAngle) * vibrateOffset;
                        mesh.position.y = originalY + vibrateOffset;
                        mesh.position.z = originalZ + Math.sin(rotateAngle) * vibrateOffset;
                    }
                }

                // 更新键的位置
                this._updateBonds();
                break;

            case 'conformationalChange':
                // 构象变化动画 - 模拟分子构象的变化，如蛋白质折叠/展开
                const confTime = this.clock.elapsedTime * speed;
                const confAmplitude = animation.amplitude || 0.5;
                const confProgress = (Math.sin(confTime * Math.PI) + 1) / 2; // 0-1循环

                // 如果没有初始化构象变化参数
                if (!this.moleculeGroup.userData.confParams) {
                    this.moleculeGroup.userData.confParams = {
                        startTime: this.clock.elapsedTime,
                        targetPositions: []
                    };

                    // 为每个原子生成目标位置
                    this.atomMeshes.forEach((mesh, i) => {
                        const atom = molecule.atoms[i];

                        // 保存原始位置
                        mesh.userData.originalPosition = new THREE.Vector3(
                            atom.position[0],
                            atom.position[1],
                            atom.position[2]
                        );

                        // 生成目标位置 - 这里可以根据分子类型设置不同的变化模式
                        let targetPos;

                        if (animation.targetMode === 'expand') {
                            // 向外扩展
                            const dir = new THREE.Vector3(
                                atom.position[0],
                                atom.position[1],
                                atom.position[2]
                            ).normalize();

                            targetPos = new THREE.Vector3(
                                atom.position[0] + dir.x * confAmplitude * 2,
                                atom.position[1] + dir.y * confAmplitude * 2,
                                atom.position[2] + dir.z * confAmplitude * 2
                            );
                        } else if (animation.targetMode === 'twist') {
                            // 扭曲变形
                            const angle = Math.PI * 0.5; // 90度扭曲
                            const height = atom.position[1];
                            const twistFactor = height / 10; // 高度越高扭曲越大

                            targetPos = new THREE.Vector3(
                                atom.position[0] * Math.cos(angle * twistFactor) - atom.position[2] * Math.sin(angle * twistFactor),
                                atom.position[1],
                                atom.position[0] * Math.sin(angle * twistFactor) + atom.position[2] * Math.cos(angle * twistFactor)
                            );
                        } else if (animation.targetMode === 'fold') {
                            // 折叠变形 - 适合蛋白质
                            const foldAxis = new THREE.Vector3(1, 0, 0); // 沿x轴折叠
                            const distFromAxis = Math.sqrt(atom.position[1]*atom.position[1] + atom.position[2]*atom.position[2]);
                            const foldAngle = Math.PI * 0.3; // 折叠角度

                            // 计算折叠后的位置
                            targetPos = new THREE.Vector3(
                                atom.position[0],
                                atom.position[1] * Math.cos(foldAngle) - atom.position[2] * Math.sin(foldAngle),
                                atom.position[1] * Math.sin(foldAngle) + atom.position[2] * Math.cos(foldAngle)
                            );

                            // 向中心压缩
                            targetPos.multiplyScalar(0.8);
                        } else {
                            // 默认：随机变形
                            targetPos = new THREE.Vector3(
                                atom.position[0] + (Math.random() - 0.5) * confAmplitude * 2,
                                atom.position[1] + (Math.random() - 0.5) * confAmplitude * 2,
                                atom.position[2] + (Math.random() - 0.5) * confAmplitude * 2
                            );
                        }

                        this.moleculeGroup.userData.confParams.targetPositions.push(targetPos);
                    });
                }

                // 应用构象变化
                const params = this.moleculeGroup.userData.confParams;

                this.atomMeshes.forEach((mesh, i) => {
                    if (mesh.userData.originalPosition && params.targetPositions[i]) {
                        const origPos = mesh.userData.originalPosition;
                        const targetPos = params.targetPositions[i];

                        // 使用平滑的过渡函数
                        const smoothProgress = this._smoothStep(0, 1, confProgress);

                        // 在原始位置和目标位置之间插值
                        mesh.position.x = origPos.x + (targetPos.x - origPos.x) * smoothProgress;
                        mesh.position.y = origPos.y + (targetPos.y - origPos.y) * smoothProgress;
                        mesh.position.z = origPos.z + (targetPos.z - origPos.z) * smoothProgress;

                        // 添加发光效果
                        if (mesh.material && this.useHighQuality) {
                            const changeIntensity = Math.abs(smoothProgress - 0.5) * 2; // 在中间位置最亮
                            mesh.material.emissive = new THREE.Color(0.3, 0.3, 0.5); // 蓝紫色发光
                            mesh.material.emissiveIntensity = 0.2 + changeIntensity * 0.8;

                            // 在构象变化过程中添加能量粒子
                            if (Math.random() < 0.002 * changeIntensity) {
                                this._createEnergyParticle(mesh.position.clone());
                            }
                        }
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'molecularDynamics':
                // 分子动力学动画 - 模拟分子在溶液中的运动
                const mdTimeFirst = this.clock.elapsedTime * speed;
                const mdAmplitudeFirst = animation.amplitude || 0.15;

                // 为每个原子添加布朗运动和分子内振动
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];

                    // 如果没有初始化，设置随机参数
                    if (!mesh.userData.mdParams) {
                        mesh.userData.mdParams = {
                            phase: Math.random() * Math.PI * 2,
                            frequency: 0.5 + Math.random() * 1.0,
                            brownianAmplitude: 0.05 + Math.random() * 0.1,
                            brownianFrequency: 0.2 + Math.random() * 0.3
                        };
                        mesh.userData.originalPosition = new THREE.Vector3(
                            atom.position[0],
                            atom.position[1],
                            atom.position[2]
                        );
                    }

                    const params = mesh.userData.mdParams;
                    const origPos = mesh.userData.originalPosition;

                    // 分子内振动 - 高频，小振幅
                    const internalVibX = Math.sin(mdTimeFirst * params.frequency * Math.PI * 2 + params.phase) * mdAmplitudeFirst * 0.3;
                    const internalVibY = Math.sin(mdTimeFirst * params.frequency * Math.PI * 2 + params.phase + Math.PI/3) * mdAmplitudeFirst * 0.3;
                    const internalVibZ = Math.sin(mdTimeFirst * params.frequency * Math.PI * 2 + params.phase + Math.PI*2/3) * mdAmplitudeFirst * 0.3;

                    // 布朗运动 - 低频，大振幅
                    const brownianX = Math.sin(mdTimeFirst * params.brownianFrequency * Math.PI * 2 + params.phase * 1.1) * params.brownianAmplitude;
                    const brownianY = Math.sin(mdTimeFirst * params.brownianFrequency * Math.PI * 2 + params.phase * 1.2 + Math.PI/4) * params.brownianAmplitude;
                    const brownianZ = Math.sin(mdTimeFirst * params.brownianFrequency * Math.PI * 2 + params.phase * 1.3 + Math.PI/2) * params.brownianAmplitude;

                    // 应用位移
                    mesh.position.x = origPos.x + internalVibX + brownianX;
                    mesh.position.y = origPos.y + internalVibY + brownianY;
                    mesh.position.z = origPos.z + internalVibZ + brownianZ;

                    // 添加发光效果
                    if (mesh.material && this.useHighQuality) {
                        // 根据原子类型和运动强度设置发光
                        let emissiveColor;
                        const motionIntensity = Math.sqrt(
                            internalVibX*internalVibX + internalVibY*internalVibY + internalVibZ*internalVibZ
                        ) * 5;

                        switch (atom.symbol) {
                            case 'O': // 氧原子 - 红色发光
                                emissiveColor = new THREE.Color(0.3, 0.1, 0.1);
                                break;
                            case 'N': // 氮原子 - 蓝色发光
                                emissiveColor = new THREE.Color(0.1, 0.1, 0.3);
                                break;
                            case 'P': // 磷原子 - 橙色发光
                                emissiveColor = new THREE.Color(0.3, 0.2, 0.1);
                                break;
                            case 'S': // 硫原子 - 黄色发光
                                emissiveColor = new THREE.Color(0.3, 0.3, 0.1);
                                break;
                            default: // 其他原子
                                emissiveColor = new THREE.Color(
                                    mesh.material.color.r * 0.2,
                                    mesh.material.color.g * 0.2,
                                    mesh.material.color.b * 0.2
                                );
                        }

                        mesh.material.emissive = emissiveColor;
                        mesh.material.emissiveIntensity = 0.2 + motionIntensity;

                        // 随机添加能量粒子
                        if (Math.random() < 0.001) { // 0.1%的概率
                            this._createEnergyParticle(mesh.position.clone());
                        }
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'crystalVibration':
                // 晶体振动动画 - 适用于氯化钠等晶体结构
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];
                    const amplitude = animation.amplitude || 0.05;
                    const frequency = animation.frequency || 0.03;

                    // 为每个原子添加随机相位，使振动不同步
                    if (!mesh.userData.vibrationPhase) {
                        mesh.userData.vibrationPhase = Math.random() * Math.PI * 2;

                        // 为不同类型的原子设置不同的振动幅度
                        if (atom.symbol === 'Na') {
                            mesh.userData.vibrationAmplitude = amplitude * 1.2; // 钠离子振动幅度稍大
                        } else if (atom.symbol === 'Cl') {
                            mesh.userData.vibrationAmplitude = amplitude * 0.8; // 氯离子振动幅度稍小
                        } else {
                            mesh.userData.vibrationAmplitude = amplitude;
                        }
                    }

                    const phase = mesh.userData.vibrationPhase;
                    const atomAmplitude = mesh.userData.vibrationAmplitude;

                    // 三维振动，每个方向有不同的相位
                    const xOffset = atomAmplitude * Math.sin(this.clock.elapsedTime * frequency * Math.PI * 2 + phase);
                    const yOffset = atomAmplitude * Math.sin(this.clock.elapsedTime * frequency * Math.PI * 2 + phase + Math.PI/3);
                    const zOffset = atomAmplitude * Math.sin(this.clock.elapsedTime * frequency * Math.PI * 2 + phase + Math.PI*2/3);

                    mesh.position.x = atom.position[0] + xOffset;
                    mesh.position.y = atom.position[1] + yOffset;
                    mesh.position.z = atom.position[2] + zOffset;

                    // 添加发光效果，振动越大，发光越强
                    if (mesh.material && this.useHighQuality) {
                        const vibrationMagnitude = Math.sqrt(xOffset*xOffset + yOffset*yOffset + zOffset*zOffset);
                        const glowIntensity = 0.1 + vibrationMagnitude * 3;

                        mesh.material.emissive = new THREE.Color(
                            mesh.material.color.r * 0.2,
                            mesh.material.color.g * 0.2,
                            mesh.material.color.b * 0.2
                        );
                        mesh.material.emissiveIntensity = glowIntensity;
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'virusFloat':
                // 病毒漂浮动画 - 适用于冠状病毒等
                const floatTime = this.clock.elapsedTime * speed;
                const floatAmplitude = animation.amplitude || 0.3;

                // 整体漂浮运动
                const globalY = Math.sin(floatTime * Math.PI) * floatAmplitude;
                const globalRotationX = floatTime * 0.1;
                const globalRotationY = floatTime * 0.2;

                if (!this.moleculeGroup.userData.originalPosition) {
                    this.moleculeGroup.userData.originalPosition = this.moleculeGroup.position.clone();
                }

                // 应用整体漂浮
                this.moleculeGroup.position.y = this.moleculeGroup.userData.originalPosition.y + globalY;
                this.moleculeGroup.rotation.x = globalRotationX;
                this.moleculeGroup.rotation.y = globalRotationY;

                // 刺突蛋白的特殊动画
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];

                    // 只对刺突蛋白应用特殊动画
                    if (atom.isSpike) {
                        // 如果没有初始化，保存原始位置
                        if (!mesh.userData.originalPosition) {
                            mesh.userData.originalPosition = mesh.position.clone();
                            mesh.userData.spikePhase = Math.random() * Math.PI * 2;
                        }

                        const spikePhase = mesh.userData.spikePhase;
                        const originalPos = mesh.userData.originalPosition;

                        // 刺突的脉动效果
                        const pulseScale = 1 + Math.sin(floatTime * Math.PI * 3 + spikePhase) * 0.1;

                        // 计算从病毒中心到刺突的方向向量
                        const dirX = originalPos.x;
                        const dirY = originalPos.y;
                        const dirZ = originalPos.z;
                        const length = Math.sqrt(dirX*dirX + dirY*dirY + dirZ*dirZ);

                        // 沿着方向向量应用脉动
                        if (length > 0) {
                            const normalizedDirX = dirX / length;
                            const normalizedDirY = dirY / length;
                            const normalizedDirZ = dirZ / length;

                            mesh.position.x = originalPos.x + normalizedDirX * (pulseScale - 1) * 2;
                            mesh.position.y = originalPos.y + normalizedDirY * (pulseScale - 1) * 2;
                            mesh.position.z = originalPos.z + normalizedDirZ * (pulseScale - 1) * 2;
                        }

                        // 添加发光效果
                        if (mesh.material && this.useHighQuality) {
                            const glowIntensity = 0.2 + Math.abs(pulseScale - 1) * 2;
                            mesh.material.emissive = new THREE.Color(
                                mesh.material.color.r * 0.3,
                                mesh.material.color.g * 0.1,
                                mesh.material.color.b * 0.1
                            );
                            mesh.material.emissiveIntensity = glowIntensity;
                        }
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'molecularDynamics':
                // 分子动力学动画 - 模拟分子在溶液中的运动
                const mdTimeValue = this.clock.elapsedTime * speed;
                const mdAmplitudeValue = animation.amplitude || 0.15;

                // 为每个原子添加布朗运动和分子内振动
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];

                    // 如果没有初始化，设置随机参数
                    if (!mesh.userData.mdParams) {
                        mesh.userData.mdParams = {
                            phase: Math.random() * Math.PI * 2,
                            frequency: 0.5 + Math.random() * 1.0,
                            brownianAmplitude: 0.05 + Math.random() * 0.1,
                            brownianFrequency: 0.2 + Math.random() * 0.3
                        };
                        mesh.userData.originalPosition = new THREE.Vector3(
                            atom.position[0],
                            atom.position[1],
                            atom.position[2]
                        );
                    }

                    const params = mesh.userData.mdParams;
                    const origPos = mesh.userData.originalPosition;

                    // 分子内振动 - 高频，小振幅
                    const internalVibX = Math.sin(mdTimeValue * params.frequency * Math.PI * 2 + params.phase) * this.mdAmplitude * 0.3;
                    const internalVibY = Math.sin(mdTimeValue * params.frequency * Math.PI * 2 + params.phase + Math.PI/3) * this.mdAmplitude * 0.3;
                    const internalVibZ = Math.sin(mdTimeValue * params.frequency * Math.PI * 2 + params.phase + Math.PI*2/3) * this.mdAmplitude * 0.3;

                    // 布朗运动 - 低频，大振幅
                    const brownianX = Math.sin(mdTimeValue * params.brownianFrequency * Math.PI * 2 + params.phase * 1.1) * params.brownianAmplitude;
                    const brownianY = Math.sin(mdTimeValue * params.brownianFrequency * Math.PI * 2 + params.phase * 1.2 + Math.PI/4) * params.brownianAmplitude;
                    const brownianZ = Math.sin(mdTimeValue * params.brownianFrequency * Math.PI * 2 + params.phase * 1.3 + Math.PI/2) * params.brownianAmplitude;

                    // 应用位移
                    mesh.position.x = origPos.x + internalVibX + brownianX;
                    mesh.position.y = origPos.y + internalVibY + brownianY;
                    mesh.position.z = origPos.z + internalVibZ + brownianZ;

                    // 添加发光效果
                    if (mesh.material && this.useHighQuality) {
                        // 根据原子类型和运动强度设置发光
                        let emissiveColor;
                        const motionIntensity = Math.sqrt(
                            internalVibX*internalVibX + internalVibY*internalVibY + internalVibZ*internalVibZ
                        ) * 5;

                        switch (atom.symbol) {
                            case 'O': // 氧原子 - 红色发光
                                emissiveColor = new THREE.Color(0.3, 0.1, 0.1);
                                break;
                            case 'N': // 氮原子 - 蓝色发光
                                emissiveColor = new THREE.Color(0.1, 0.1, 0.3);
                                break;
                            case 'P': // 磷原子 - 橙色发光
                                emissiveColor = new THREE.Color(0.3, 0.2, 0.1);
                                break;
                            case 'S': // 硫原子 - 黄色发光
                                emissiveColor = new THREE.Color(0.3, 0.3, 0.1);
                                break;
                            default: // 其他原子
                                emissiveColor = new THREE.Color(
                                    mesh.material.color.r * 0.2,
                                    mesh.material.color.g * 0.2,
                                    mesh.material.color.b * 0.2
                                );
                        }

                        mesh.material.emissive = emissiveColor;
                        mesh.material.emissiveIntensity = 0.2 + motionIntensity;

                        // 随机添加能量粒子
                        if (Math.random() < 0.001) { // 0.1%的概率
                            this._createEnergyParticle(mesh.position.clone());
                        }
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            case 'phageAttack':
                // 噬菌体攻击动画
                const attackTime = this.clock.elapsedTime * speed;
                const attackAmplitude = animation.amplitude || 0.2;

                // 整体脉动
                const pulseScale = 1 + Math.sin(attackTime * Math.PI * 2) * attackAmplitude * 0.5;

                // 头部和尾部的不同动画
                this.atomMeshes.forEach((mesh, i) => {
                    const atom = molecule.atoms[i];

                    // 如果没有初始化，保存原始位置
                    if (!mesh.userData.originalPosition) {
                        mesh.userData.originalPosition = mesh.position.clone();
                    }

                    const originalPos = mesh.userData.originalPosition;

                    if (atom.isHead) {
                        // 头部脉动
                        const headPulse = 1 + Math.sin(attackTime * Math.PI * 2) * attackAmplitude;

                        // 计算从中心到头部的方向向量
                        const dirX = originalPos.x;
                        const dirY = originalPos.y - 15; // 头部中心在y=15
                        const dirZ = originalPos.z;
                        const length = Math.sqrt(dirX*dirX + dirY*dirY + dirZ*dirZ);

                        // 沿着方向向量应用脉动
                        if (length > 0) {
                            const normalizedDirX = dirX / length;
                            const normalizedDirY = dirY / length;
                            const normalizedDirZ = dirZ / length;

                            mesh.position.x = originalPos.x + normalizedDirX * (headPulse - 1) * 2;
                            mesh.position.y = originalPos.y + normalizedDirY * (headPulse - 1) * 2;
                            mesh.position.z = originalPos.z + normalizedDirZ * (headPulse - 1) * 2;
                        }
                    } else if (atom.isDNA) {
                        // DNA旋转
                        const dnaRotation = attackTime * Math.PI;
                        const dnaRadius = 4 * pulseScale;

                        // 获取原始角度和高度
                        if (!mesh.userData.dnaParams) {
                            const x = originalPos.x;
                            const z = originalPos.z;
                            const angle = Math.atan2(z, x);
                            const height = originalPos.y;
                            const radius = Math.sqrt(x*x + z*z);

                            mesh.userData.dnaParams = {
                                angle: angle,
                                height: height,
                                radius: radius
                            };
                        }

                        const params = mesh.userData.dnaParams;

                        // 应用旋转和脉动
                        mesh.position.x = Math.cos(params.angle + dnaRotation) * params.radius * pulseScale;
                        mesh.position.z = Math.sin(params.angle + dnaRotation) * params.radius * pulseScale;
                        mesh.position.y = params.height;
                    } else if (atom.isTail) {
                        // 尾部波动
                        const tailWave = Math.sin(attackTime * Math.PI * 4 + originalPos.y * 0.1) * attackAmplitude;
                        mesh.position.x = originalPos.x + tailWave;
                    } else if (atom.isFiber) {
                        // 纤维摆动
                        const fiberWave = Math.sin(attackTime * Math.PI * 3 + originalPos.y * 0.2) * attackAmplitude * 1.5;

                        // 计算从尾部到纤维的方向向量
                        const dirX = originalPos.x;
                        const dirZ = originalPos.z;
                        const length = Math.sqrt(dirX*dirX + dirZ*dirZ);

                        // 垂直于方向向量的摆动
                        if (length > 0) {
                            const normalizedDirX = dirX / length;
                            const normalizedDirZ = dirZ / length;

                            // 垂直方向
                            const perpX = -normalizedDirZ;
                            const perpZ = normalizedDirX;

                            mesh.position.x = originalPos.x + perpX * fiberWave;
                            mesh.position.z = originalPos.z + perpZ * fiberWave;
                        }
                    } else if (atom.isSpike) {
                        // 尖刺伸缩
                        const spikeExtend = Math.sin(attackTime * Math.PI * 2) * attackAmplitude * 3;
                        mesh.position.y = originalPos.y - spikeExtend;

                        // 添加发光效果
                        if (mesh.material && this.useHighQuality) {
                            const glowIntensity = 0.3 + Math.abs(spikeExtend) * 0.5;
                            mesh.material.emissive = new THREE.Color(0.5, 0, 0);
                            mesh.material.emissiveIntensity = glowIntensity;
                        }
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;

            default:
                // 默认使用简单的振动动画
                this.atomMeshes.forEach((mesh, i) => {
                    if (i < molecule.atoms.length) {
                        const atom = molecule.atoms[i];
                        const amplitude = 0.1;
                        const frequency = 0.05;

                        // 简单的振动效果
                        const offset = amplitude * Math.sin(this.clock.elapsedTime * frequency * Math.PI * 2);

                        mesh.position.x = atom.position[0] + offset;
                        mesh.position.y = atom.position[1] + offset;
                        mesh.position.z = atom.position[2] + offset;
                    }
                });

                // 更新键的位置
                this._updateBonds();
                break;
        }
        } catch (error) {
            console.error('动画更新失败:', error);
        }
    }

    /**
     * 创建能量释放粒子
     * @private
     * @param {THREE.Vector3} position - 粒子生成位置
     */
    _createEnergyParticle(position) {
        // 创建一个小球体作为粒子
        const geometry = new THREE.SphereGeometry(0.1, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 1.0
        });

        const particle = new THREE.Mesh(geometry, material);
        particle.position.copy(position);

        // 随机速度
        particle.velocity = new THREE.Vector3(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
        );

        // 生命周期
        particle.life = 1.0;
        particle.maxLife = 1.0;
        particle.respawn = false;

        this.scene.add(particle);
        this.particles.push(particle);
    }

    /**
     * 创建反应粒子
     * @private
     * @param {THREE.Vector3} position - 粒子生成位置
     */
    _createReactionParticle(position) {
        // 创建一个小球体作为粒子
        const geometry = new THREE.SphereGeometry(0.05, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: Math.random() > 0.5 ? 0x00ffff : 0xff00ff, // 随机颜色
            transparent: true,
            opacity: 1.0
        });

        const particle = new THREE.Mesh(geometry, material);
        particle.position.copy(position);

        // 随机速度，但主要向上
        particle.velocity = new THREE.Vector3(
            (Math.random() - 0.5) * 1,
            Math.random() * 2,
            (Math.random() - 0.5) * 1
        );

        // 生命周期
        particle.life = 0.5 + Math.random() * 0.5;
        particle.maxLife = particle.life;
        particle.respawn = false;

        this.scene.add(particle);
        this.particles.push(particle);
    }

    /**
     * 平滑过渡函数
     * @private
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {number} value - 输入值 (0-1)
     * @returns {number} 平滑过渡的值
     */
    _smoothStep(min, max, value) {
        const x = Math.max(0, Math.min(1, (value - min) / (max - min)));
        return x * x * (3 - 2 * x);
    }

    /**
     * 更新键的位置和方向
     * @private
     */
    _updateBonds() {
        if (!this.currentMolecule) return;

        this.bondMeshes.forEach((bondMesh, i) => {
            const bond = this.currentMolecule.bonds[i];
            const startAtom = this.atomMeshes[bond.start];
            const endAtom = this.atomMeshes[bond.end];

            if (startAtom && endAtom) {
                // 计算键的中点
                const midpoint = new THREE.Vector3().addVectors(
                    startAtom.position,
                    endAtom.position
                ).multiplyScalar(0.5);

                // 设置键的位置为中点
                bondMesh.position.copy(midpoint);

                // 计算键的方向
                const direction = new THREE.Vector3().subVectors(
                    endAtom.position,
                    startAtom.position
                );

                // 计算键的长度
                const length = direction.length();

                // 设置键的缩放，使其连接两个原子
                bondMesh.scale.y = length;

                // 设置键的旋转，使其指向正确的方向
                bondMesh.quaternion.setFromUnitVectors(
                    new THREE.Vector3(0, 1, 0),
                    direction.normalize()
                );
            }
        });
    }

    /**
     * 加载并显示分子
     * @param {Object} molecule - 分子数据
     */
    loadMolecule(molecule) {
        if (!molecule) {
            console.error('无法加载分子：分子数据为空');
            return;
        }

        console.log(`加载分子: ${molecule.name || '未命名分子'}`);

        // 检查分子数据是否有效
        if (!molecule.atoms || !Array.isArray(molecule.atoms) || molecule.atoms.length === 0) {
            console.error('无效的分子数据：缺少原子数组或原子数组为空');
            if (this.fallbackRenderer) {
                this.fallbackRenderer.activate('无效的分子数据，无法渲染');
            }
            return;
        }

        // 如果渲染失败，使用备用渲染器
        if (this.renderingFailed && this.fallbackRenderer) {
            console.log('使用备用渲染器显示分子');
            this.fallbackRenderer.renderMolecule(molecule);
            this.currentMolecule = molecule;
            return;
        }

        try {
            // 清除当前分子
            this.clearMolecule();

            this.currentMolecule = molecule;

            // 创建一个组来容纳分子的所有部分
            this.moleculeGroup = new THREE.Group();
            this.scene.add(this.moleculeGroup);

            // 确保bonds数组存在
            if (!molecule.bonds) {
                console.warn('分子数据缺少键信息，将创建空的键数组');
                molecule.bonds = [];
            }

            // 根据显示模式渲染分子
            switch (this.displayMode) {
                case 'ball-stick':
                    this._renderBallAndStick(molecule);
                    break;
                case 'space-filling':
                    this._renderSpaceFilling(molecule);
                    break;
                case 'cartoon':
                    this._renderCartoon(molecule);
                    break;
                case 'wireframe':
                    this._renderWireframe(molecule);
                    break;
                default:
                    console.log('使用默认的球棍模型渲染');
                    this._renderBallAndStick(molecule);
            }

            // 居中分子
            this._centerMolecule();

            console.log(`分子 ${molecule.name || '未命名分子'} 加载成功，包含 ${molecule.atoms.length} 个原子和 ${molecule.bonds.length} 个键`);
        } catch (error) {
            console.error('加载分子时发生错误:', error);
            this.renderingFailed = true;

            // 激活备用渲染器
            if (this.fallbackRenderer) {
                this.fallbackRenderer.activate('3D渲染失败，已切换到2D模式');
                this.fallbackRenderer.renderMolecule(molecule);
            }
        }
    }

    /**
     * 渲染球棍模型
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderBallAndStick(molecule) {
        // 渲染原子
        molecule.atoms.forEach(atom => {
            const radius = this._getAtomRadius(atom.symbol) * 0.4;

            // 根据质量设置不同的几何体细节
            const segments = this.useHighQuality ? 32 : 16;
            const geometry = new THREE.SphereGeometry(radius, segments, segments);

            // 创建高质量材质
            let material;
            if (this.useHighQuality) {
                // 使用物理材质，更真实的光照效果
                material = new THREE.MeshStandardMaterial({
                    color: atom.color || this._getAtomColor(atom.symbol),
                    metalness: 0.3,
                    roughness: 0.2,
                    envMapIntensity: 1.0,
                    emissive: new THREE.Color(0x000000),
                    emissiveIntensity: 0.2
                });

                // 添加法线贴图，增加表面细节
                if (atom.symbol !== 'H') { // 对于非氢原子，添加更多细节
                    const bumpScale = this._getAtomRadius(atom.symbol) * 0.05;
                    material.bumpScale = bumpScale;
                }
            } else {
                // 使用简单的Phong材质，性能更好
                material = new THREE.MeshPhongMaterial({
                    color: atom.color || this._getAtomColor(atom.symbol),
                    shininess: 100
                });
            }

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(...atom.position);

            // 添加阴影
            if (this.useHighQuality) {
                mesh.castShadow = true;
                mesh.receiveShadow = true;
            }

            this.atomMeshes.push(mesh);
            this.moleculeGroup.add(mesh);
        });

        // 渲染键
        molecule.bonds.forEach(bond => {
            const startAtom = molecule.atoms[bond.start];
            const endAtom = molecule.atoms[bond.end];

            const startPos = new THREE.Vector3(...startAtom.position);
            const endPos = new THREE.Vector3(...endAtom.position);

            // 计算键的中点和长度
            const midpoint = new THREE.Vector3().addVectors(startPos, endPos).multiplyScalar(0.5);
            const direction = new THREE.Vector3().subVectors(endPos, startPos);
            const length = direction.length();

            // 创建键的几何体，根据质量设置不同的细节
            const radialSegments = this.useHighQuality ? 16 : 8;
            const geometry = new THREE.CylinderGeometry(0.2, 0.2, 1, radialSegments);

            // 创建材质
            let material;
            if (this.useHighQuality) {
                // 使用物理材质
                material = new THREE.MeshStandardMaterial({
                    color: bond.color || 0xffffff,
                    metalness: 0.1,
                    roughness: 0.3,
                    transparent: bond.dashed,
                    opacity: bond.dashed ? 0.7 : 1
                });
            } else {
                // 使用简单的Phong材质
                material = new THREE.MeshPhongMaterial({
                    color: bond.color || 0xffffff,
                    shininess: 100,
                    transparent: bond.dashed,
                    opacity: bond.dashed ? 0.7 : 1
                });
            }

            if (bond.dashed) {
                material.dashSize = 0.5;
                material.gapSize = 0.2;
            }

            const mesh = new THREE.Mesh(geometry, material);

            // 设置键的位置为中点
            mesh.position.copy(midpoint);

            // 设置键的缩放，使其连接两个原子
            mesh.scale.y = length;

            // 设置键的旋转，使其指向正确的方向
            mesh.quaternion.setFromUnitVectors(
                new THREE.Vector3(0, 1, 0),
                direction.normalize()
            );

            // 添加阴影
            if (this.useHighQuality) {
                mesh.castShadow = true;
                mesh.receiveShadow = true;
            }

            this.bondMeshes.push(mesh);
            this.moleculeGroup.add(mesh);
        });
    }

    /**
     * 渲染空间填充模型
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderSpaceFilling(molecule) {
        // 渲染原子为更大的球体
        molecule.atoms.forEach(atom => {
            const radius = this._getAtomRadius(atom.symbol);
            const geometry = new THREE.SphereGeometry(radius, 32, 32);
            const material = new THREE.MeshPhongMaterial({
                color: atom.color || this._getAtomColor(atom.symbol),
                shininess: 100
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(...atom.position);

            this.atomMeshes.push(mesh);
            this.moleculeGroup.add(mesh);
        });

        // 空间填充模型不显示键
    }

    /**
     * 渲染卡通模型
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderCartoon(molecule) {
        // 对于DNA和蛋白质，使用特殊的卡通表示
        if (molecule.name.includes('DNA')) {
            this._renderDNACartoon(molecule);
        } else if (molecule.name.includes('蛋白质')) {
            this._renderProteinCartoon(molecule);
        } else {
            // 对于其他分子，使用简化的球棍模型
            this._renderBallAndStick(molecule);
        }
    }

    /**
     * 渲染DNA卡通模型
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderDNACartoon(molecule) {
        // 创建两条螺旋骨架
        const strand1Points = [];
        const strand2Points = [];
        const basePairConnections = [];

        // 分析原子位置，找出骨架和碱基
        molecule.atoms.forEach((atom, i) => {
            // 使用strand属性来确定原子属于哪条链
            if (atom.strand !== undefined) {
                // 只使用磷酸原子来构建骨架，使曲线更平滑
                if (atom.symbol === 'P' && atom.strand === 0) {
                    strand1Points.push(new THREE.Vector3(...atom.position));
                } else if (atom.symbol === 'P' && atom.strand === 1) {
                    strand2Points.push(new THREE.Vector3(...atom.position));
                }
                // 收集所有碱基信息
                else if (atom.symbol === 'A' || atom.symbol === 'G' || atom.symbol === 'T' || atom.symbol === 'C') {
                    if (!basePairConnections[atom.pairIndex]) {
                        basePairConnections[atom.pairIndex] = [];
                    }
                    basePairConnections[atom.pairIndex].push({
                        position: new THREE.Vector3(...atom.position),
                        strand: atom.strand,
                        symbol: atom.symbol
                    });
                }
            } else {
                // 兼容旧版本数据，根据索引判断
                if (atom.symbol === 'P') {
                    if (i < molecule.atoms.length / 2) {
                        strand1Points.push(new THREE.Vector3(...atom.position));
                    } else {
                        strand2Points.push(new THREE.Vector3(...atom.position));
                    }
                } else if (atom.symbol === 'A' || atom.symbol === 'G' || atom.symbol === 'T' || atom.symbol === 'C' || atom.symbol === 'N') {
                    const pairIndex = Math.floor(i / 6); // 估计碱基对索引
                    if (!basePairConnections[pairIndex]) {
                        basePairConnections[pairIndex] = [];
                    }
                    basePairConnections[pairIndex].push({
                        position: new THREE.Vector3(...atom.position),
                        strand: i < molecule.atoms.length / 2 ? 0 : 1,
                        symbol: atom.symbol
                    });
                }
            }
        });

        // 如果点太少，无法形成平滑曲线，则添加插值点
        if (strand1Points.length < 4 && strand1Points.length > 0) {
            const interpolatedPoints1 = this._interpolatePoints(strand1Points);
            strand1Points.length = 0;
            strand1Points.push(...interpolatedPoints1);
        }

        if (strand2Points.length < 4 && strand2Points.length > 0) {
            const interpolatedPoints2 = this._interpolatePoints(strand2Points);
            strand2Points.length = 0;
            strand2Points.push(...interpolatedPoints2);
        }

        // 对点进行排序，确保平滑的曲线
        strand1Points.sort((a, b) => a.y - b.y);
        strand2Points.sort((a, b) => a.y - b.y);

        // 创建第一条链的管道 (蓝色)
        if (strand1Points.length > 2) {
            // 使用平滑的曲线
            const curve1 = new THREE.CatmullRomCurve3(strand1Points);
            curve1.tension = 0.3; // 调整曲线张力，使其更平滑

            const tubeSegments = Math.max(30, strand1Points.length * 4);
            const geometry1 = new THREE.TubeGeometry(curve1, tubeSegments, 0.6, 16, false);

            // 使用更好的材质
            const material1 = this.useHighQuality ?
                new THREE.MeshStandardMaterial({
                    color: 0x3498db,
                    roughness: 0.3,
                    metalness: 0.2,
                    emissive: 0x3498db,
                    emissiveIntensity: 0.1
                }) :
                new THREE.MeshPhongMaterial({
                    color: 0x3498db,
                    shininess: 100
                });

            const tube1 = new THREE.Mesh(geometry1, material1);
            if (this.useHighQuality) {
                tube1.castShadow = true;
                tube1.receiveShadow = true;
            }
            this.moleculeGroup.add(tube1);
        }

        // 创建第二条链的管道 (红色)
        if (strand2Points.length > 2) {
            const curve2 = new THREE.CatmullRomCurve3(strand2Points);
            curve2.tension = 0.3;

            const tubeSegments = Math.max(30, strand2Points.length * 4);
            const geometry2 = new THREE.TubeGeometry(curve2, tubeSegments, 0.6, 16, false);

            const material2 = this.useHighQuality ?
                new THREE.MeshStandardMaterial({
                    color: 0xe74c3c,
                    roughness: 0.3,
                    metalness: 0.2,
                    emissive: 0xe74c3c,
                    emissiveIntensity: 0.1
                }) :
                new THREE.MeshPhongMaterial({
                    color: 0xe74c3c,
                    shininess: 100
                });

            const tube2 = new THREE.Mesh(geometry2, material2);
            if (this.useHighQuality) {
                tube2.castShadow = true;
                tube2.receiveShadow = true;
            }
            this.moleculeGroup.add(tube2);
        }

        // 添加碱基对连接
        Object.values(basePairConnections).forEach(pair => {
            if (pair && pair.length >= 2) {
                // 找到两条链上的碱基
                const strand1Base = pair.find(b => b.strand === 0);
                const strand2Base = pair.find(b => b.strand === 1);

                if (strand1Base && strand2Base) {
                    const start = strand1Base.position;
                    const end = strand2Base.position;

                    // 创建连接碱基对的圆柱体
                    const direction = new THREE.Vector3().subVectors(end, start);
                    const length = direction.length();
                    const midpoint = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5);

                    // 根据碱基类型选择颜色
                    let color;
                    if ((strand1Base.symbol === 'A' && strand2Base.symbol === 'T') ||
                        (strand1Base.symbol === 'T' && strand2Base.symbol === 'A')) {
                        color = 0x2ecc71; // A-T 对，绿色
                    } else if ((strand1Base.symbol === 'G' && strand2Base.symbol === 'C') ||
                               (strand1Base.symbol === 'C' && strand2Base.symbol === 'G')) {
                        color = 0xf1c40f; // G-C 对，黄色
                    } else {
                        color = 0x95a5a6; // 其他，灰色
                    }

                    const geometry = new THREE.CylinderGeometry(0.2, 0.2, length, 8);
                    const material = new THREE.MeshPhongMaterial({
                        color: color,
                        shininess: 100,
                        transparent: true,
                        opacity: 0.8
                    });

                    const cylinder = new THREE.Mesh(geometry, material);
                    cylinder.position.copy(midpoint);

                    cylinder.quaternion.setFromUnitVectors(
                        new THREE.Vector3(0, 1, 0),
                        direction.normalize()
                    );

                    this.moleculeGroup.add(cylinder);

                    // 添加碱基表示
                    if (this.useHighQuality) {
                        // 在碱基位置添加小球体表示碱基
                        [strand1Base, strand2Base].forEach(base => {
                            const baseGeometry = new THREE.SphereGeometry(0.4, 16, 16);
                            const baseColor = base.symbol === 'A' ? 0x3498db :
                                             base.symbol === 'T' ? 0xe74c3c :
                                             base.symbol === 'G' ? 0x2ecc71 :
                                             base.symbol === 'C' ? 0xf1c40f : 0xffffff;

                            const baseMaterial = new THREE.MeshPhongMaterial({
                                color: baseColor,
                                shininess: 100
                            });

                            const baseSphere = new THREE.Mesh(baseGeometry, baseMaterial);
                            baseSphere.position.copy(base.position);
                            this.moleculeGroup.add(baseSphere);
                        });
                    }
                }
            }
        });
    }

    /**
     * 插值点，创建更平滑的曲线
     * @private
     * @param {Array} points - 原始点数组
     * @returns {Array} 插值后的点数组
     */
    _interpolatePoints(points) {
        if (points.length < 2) return points;

        const result = [];
        result.push(points[0]); // 添加第一个点

        // 在每两个点之间插入额外的点
        for (let i = 0; i < points.length - 1; i++) {
            const p1 = points[i];
            const p2 = points[i + 1];

            // 添加5个插值点
            for (let t = 0.2; t < 1; t += 0.2) {
                const x = p1.x + (p2.x - p1.x) * t;
                const y = p1.y + (p2.y - p1.y) * t;
                const z = p1.z + (p2.z - p1.z) * t;

                result.push(new THREE.Vector3(x, y, z));
            }

            result.push(p2); // 添加终点
        }

        return result;
    }

    /**
     * 渲染蛋白质卡通模型
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderProteinCartoon(molecule) {
        // 找出主链原子
        const backbonePoints = [];

        // 分析原子位置，找出主链
        molecule.atoms.forEach((atom, i) => {
            if (atom.symbol === 'C' || atom.symbol === 'N' || atom.symbol === 'O') {
                // 只添加每三个原子中的一个，以简化骨架
                if (i % 3 === 0) {
                    backbonePoints.push(new THREE.Vector3(...atom.position));
                }
            }
        });

        // 创建主链的管道
        if (backbonePoints.length > 1) {
            const curve = new THREE.CatmullRomCurve3(backbonePoints);
            const geometry = new THREE.TubeGeometry(curve, backbonePoints.length * 2, 1.2, 8, false);
            const material = new THREE.MeshPhongMaterial({
                color: 0x9b59b6,
                shininess: 100,
                transparent: true,
                opacity: 0.8
            });
            const tube = new THREE.Mesh(geometry, material);
            this.moleculeGroup.add(tube);

            // 为了实现折叠动画，将管道分段
            if (molecule.animation && molecule.animation.type === 'folding') {
                // 移除整体管道
                this.moleculeGroup.remove(tube);

                // 创建分段管道
                const segmentLength = Math.max(2, Math.floor(backbonePoints.length / 10));

                for (let i = 0; i < backbonePoints.length - segmentLength; i += segmentLength) {
                    const segmentPoints = backbonePoints.slice(i, i + segmentLength + 1);
                    const segmentCurve = new THREE.CatmullRomCurve3(segmentPoints);
                    const segmentGeometry = new THREE.TubeGeometry(
                        segmentCurve, segmentPoints.length * 2, 1.2, 8, false
                    );
                    const segmentMaterial = new THREE.MeshPhongMaterial({
                        color: 0x9b59b6,
                        shininess: 100,
                        transparent: true,
                        opacity: 0.8
                    });
                    const segmentTube = new THREE.Mesh(segmentGeometry, segmentMaterial);

                    // 创建一个组来容纳这个段
                    const segmentGroup = new THREE.Group();
                    segmentGroup.add(segmentTube);

                    // 设置段的位置为第一个点
                    if (i > 0) {
                        segmentGroup.position.copy(backbonePoints[i]);
                        // 将所有点相对于第一个点进行偏移
                        segmentTube.position.sub(backbonePoints[i]);
                    }

                    this.moleculeGroup.add(segmentGroup);
                }
            }
        }

        // 添加侧链表示
        molecule.atoms.forEach((atom, i) => {
            if (atom.symbol === 'R') {
                const position = new THREE.Vector3(...atom.position);
                const geometry = new THREE.SphereGeometry(0.6, 16, 16);
                const material = new THREE.MeshPhongMaterial({
                    color: 0xf1c40f,
                    shininess: 100
                });
                const sphere = new THREE.Mesh(geometry, material);
                sphere.position.copy(position);
                this.moleculeGroup.add(sphere);
            }
        });
    }

    /**
     * 渲染线框模型
     * @private
     * @param {Object} molecule - 分子数据
     */
    _renderWireframe(molecule) {
        // 创建线框几何体
        const geometry = new THREE.BufferGeometry();
        const positions = [];

        // 添加键作为线段
        molecule.bonds.forEach(bond => {
            const startAtom = molecule.atoms[bond.start];
            const endAtom = molecule.atoms[bond.end];

            positions.push(...startAtom.position);
            positions.push(...endAtom.position);
        });

        geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

        const material = new THREE.LineBasicMaterial({
            color: 0xffffff,
            linewidth: 1
        });

        const wireframe = new THREE.LineSegments(geometry, material);
        this.moleculeGroup.add(wireframe);

        // 添加原子标签
        molecule.atoms.forEach(atom => {
            const position = new THREE.Vector3(...atom.position);
            const geometry = new THREE.SphereGeometry(0.2, 16, 16);
            const material = new THREE.MeshBasicMaterial({
                color: atom.color || this._getAtomColor(atom.symbol)
            });
            const sphere = new THREE.Mesh(geometry, material);
            sphere.position.copy(position);
            this.moleculeGroup.add(sphere);
        });
    }

    /**
     * 居中分子
     * @private
     */
    _centerMolecule() {
        if (!this.moleculeGroup) return;

        // 计算分子的边界框
        const box = new THREE.Box3().setFromObject(this.moleculeGroup);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        // 将分子移动到原点
        this.moleculeGroup.position.sub(center);

        // 调整相机位置
        const maxDim = Math.max(size.x, size.y, size.z);
        const fov = this.camera.fov * (Math.PI / 180);
        let cameraZ = Math.abs(maxDim / Math.sin(fov / 2));

        // 确保最小可见尺寸
        if (maxDim < 1) {
            cameraZ = 5; // 默认距离
        } else {
            // 添加一些边距
            cameraZ *= 1.5;
        }

        this.camera.position.z = cameraZ;

        // 更新相机的近剪裁面和远剪裁面
        const near = Math.max(0.1, cameraZ / 100);
        const far = Math.min(10000, cameraZ * 100);
        this.camera.near = near;
        this.camera.far = far;
        this.camera.updateProjectionMatrix();

        // 重置控制器
        this.controls.target.set(0, 0, 0);
        this.controls.update();

        // 强制渲染一帧
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    /**
     * 清除当前分子
     */
    clearMolecule() {
        try {
            if (this.moleculeGroup) {
                console.log('清除当前分子...');

                // 移除所有网格
                while (this.moleculeGroup.children && this.moleculeGroup.children.length > 0) {
                    const child = this.moleculeGroup.children[0];
                    this.moleculeGroup.remove(child);

                    try {
                        // 释放几何体和材质
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(material => {
                                    if (material && typeof material.dispose === 'function') {
                                        material.dispose();
                                    }
                                });
                            } else if (typeof child.material.dispose === 'function') {
                                child.material.dispose();
                            }
                        }
                    } catch (e) {
                        console.warn('清除资源时出错:', e);
                    }
                }

                // 从场景中移除分子组
                if (this.scene) {
                    this.scene.remove(this.moleculeGroup);
                }
                this.moleculeGroup = null;
            }

            // 清空网格数组
            this.atomMeshes = [];
            this.bondMeshes = [];

            // 清空动画混合器
            this.animationMixers = [];

            // 清空粒子
            if (this.particles && this.particles.length > 0) {
                this.particles.forEach(particle => {
                    if (this.scene) {
                        this.scene.remove(particle);
                    }
                    if (particle.geometry) particle.geometry.dispose();
                    if (particle.material) particle.material.dispose();
                });
                this.particles = [];
            }

            // 重置当前分子
            this.currentMolecule = null;

            console.log('分子清除完成');
        } catch (error) {
            console.error('清除分子时发生错误:', error);
        }
    }

    /**
     * 设置显示模式
     * @param {string} mode - 显示模式
     */
    setDisplayMode(mode) {
        if (this.displayMode === mode) return;

        this.displayMode = mode;

        // 如果当前有分子，重新加载它以应用新的显示模式
        if (this.currentMolecule) {
            this.loadMolecule(this.currentMolecule);
        }
    }

    /**
     * 设置动画速度
     * @param {number} speed - 动画速度 (0-1)
     */
    setAnimationSpeed(speed) {
        this.animationSpeed = Math.max(0, Math.min(1, speed));
    }

    /**
     * 播放/暂停动画
     * @param {boolean} play - 是否播放
     */
    setPlaying(play) {
        this.isPlaying = play;
    }

    /**
     * 切换播放/暂停状态
     */
    togglePlayPause() {
        this.isPlaying = !this.isPlaying;
        return this.isPlaying;
    }

    /**
     * 重置视图
     */
    resetView() {
        if (this.currentMolecule) {
            this._centerMolecule();
        }
    }

    /**
     * 设置动画类型
     * @param {string} type - 动画类型
     * @param {Object} options - 动画选项
     */
    setAnimationType(type, options = {}) {
        if (!this.currentMolecule) return;

        // 默认动画设置
        this.animation = {
            type: type,
            speed: options.speed || 1.0,
            amplitude: options.amplitude || 0.1,
            frequency: options.frequency || 0.05,
            ...options
        };

        // 如果是分子动力学动画，重置所有原子的动力学参数
        if (type === 'molecularDynamics') {
            this.atomMeshes.forEach(mesh => {
                mesh.userData.mdParams = null;
            });
        }

        console.log(`动画类型已设置为: ${type}，选项:`, this.animation);
    }

    /**
     * 获取原子的颜色
     * @private
     * @param {string} symbol - 原子符号
     * @returns {number} 颜色值
     */
    _getAtomColor(symbol) {
        const colors = {
            'H': 0xffffff,  // 白色
            'C': 0x808080,  // 灰色
            'N': 0x0000ff,  // 蓝色
            'O': 0xff0000,  // 红色
            'P': 0xffa500,  // 橙色
            'S': 0xffff00,  // 黄色
            'Cl': 0x00ff00, // 绿色
            'Na': 0x0000ff, // 蓝色
            'Mg': 0x00ff00, // 绿色
            'Fe': 0xffa500, // 橙色
            'R': 0x00ff00   // 绿色 (用于表示侧链)
        };

        return colors[symbol] || 0xcccccc;
    }

    /**
     * 获取原子的半径
     * @private
     * @param {string} symbol - 原子符号
     * @returns {number} 半径值
     */
    _getAtomRadius(symbol) {
        const radii = {
            'H': 0.5,
            'C': 0.8,
            'N': 0.7,
            'O': 0.7,
            'P': 1.0,
            'S': 1.0,
            'Cl': 1.0,
            'Na': 1.2,
            'Mg': 1.2,
            'Fe': 1.2,
            'R': 0.8  // 用于表示侧链
        };

        return radii[symbol] || 0.8;
    }

    /**
     * 检查WebGL支持
     * @private
     * @returns {boolean} 是否支持WebGL
     */
    _checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return !!gl;
        } catch (e) {
            return false;
        }
    }
}
