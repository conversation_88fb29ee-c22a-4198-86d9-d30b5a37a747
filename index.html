<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分子生物学动态可视化 - 增强版</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="css/styles.css">

    <!-- 加载进度指示器 -->
    <div id="loading-progress" style="position: fixed; top: 0; left: 0; width: 100%; height: 4px; background: #ddd; z-index: 9999;">
        <div id="loading-bar" style="height: 100%; width: 0%; background: #3498db; transition: width 0.3s;"></div>
    </div>

    <!-- 错误处理 -->
    <script>
        window.addEventListener('error', function(event) {
            console.error('捕获到错误:', event.message, 'at', event.filename, ':', event.lineno);

            // 显示错误信息给用户
            if (!window.errorShown) {
                window.errorShown = true;
                const errorDiv = document.createElement('div');
                errorDiv.style.position = 'fixed';
                errorDiv.style.top = '10px';
                errorDiv.style.left = '50%';
                errorDiv.style.transform = 'translateX(-50%)';
                errorDiv.style.backgroundColor = 'rgba(255,0,0,0.8)';
                errorDiv.style.color = 'white';
                errorDiv.style.padding = '10px 20px';
                errorDiv.style.borderRadius = '5px';
                errorDiv.style.zIndex = '9999';
                errorDiv.textContent = '加载出错，请检查控制台或刷新页面重试';
                document.body.appendChild(errorDiv);

                // 5秒后自动隐藏
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 5000);
            }
        });
    </script>



    <!-- Three.js库 - 优化加载策略，优先使用CDN -->
    <script>
        function loadScript(src, onSuccess, onError) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    onSuccess && onSuccess();
                    resolve();
                };
                script.onerror = () => {
                    onError && onError();
                    reject(new Error(`脚本加载失败: ${src}`));
                };
                document.head.appendChild(script);
            });
        }

        async function loadThreeJS() {
            try {
                // 更新加载进度
                document.getElementById('loading-bar').style.width = '20%';
                
                // 优先使用CDN加载完整版Three.js，提高兼容性
                await loadScript('https://cdnjs.cloudflare.com/ajax/libs/three.js/r132/three.min.js',
                    () => console.log('Three.js 主CDN加载成功'),
                    () => console.log('Three.js 主CDN加载失败，尝试备用CDN')
                ).catch(async () => {
                    // 主CDN失败时尝试备用CDN
                    await loadScript('https://unpkg.com/three@0.132.2/build/three.min.js',
                        () => console.log('Three.js 备用CDN加载成功'),
                        () => console.log('Three.js 备用CDN加载失败，尝试本地文件')
                    ).catch(async () => {
                        // CDN都失败时加载本地文件
                        await loadScript('js/lib/three.min.js',
                            () => console.log('Three.js本地文件加载成功'),
                            () => console.error('Three.js加载完全失败')
                        );
                    });
                });

                document.getElementById('loading-bar').style.width = '40%';
                
                // 加载OrbitControls
                await loadScript('https://unpkg.com/three@0.132.2/examples/js/controls/OrbitControls.js',
                    () => console.log('OrbitControls CDN加载成功'),
                    () => console.log('OrbitControls CDN加载失败，尝试本地文件')
                ).catch(async () => {
                    await loadScript('js/lib/OrbitControls.js',
                        () => console.log('OrbitControls本地文件加载成功'),
                        () => console.error('OrbitControls加载完全失败')
                    );
                });

                document.getElementById('loading-bar').style.width = '60%';
                
                // 加载PDBLoader
                await loadScript('https://unpkg.com/three@0.132.2/examples/js/loaders/PDBLoader.js',
                    () => console.log('PDBLoader CDN加载成功'),
                    () => console.log('PDBLoader CDN加载失败，尝试本地文件')
                ).catch(async () => {
                    await loadScript('js/lib/PDBLoader.js',
                        () => console.log('PDBLoader本地文件加载成功'),
                        () => console.error('PDBLoader加载完全失败')
                    );
                });

                document.getElementById('loading-bar').style.width = '80%';
                
                // 加载完成
                return true;
            } catch (error) {
                console.error('Three.js核心库加载失败:', error);
                
                // 显示错误信息
                const loadingProgress = document.getElementById('loading-progress');
                if (loadingProgress) {
                    loadingProgress.style.display = 'none';
                }
                
                // 显示错误提示
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                    background: rgba(0,0,0,0.9); color: white; padding: 30px; border-radius: 10px;
                    text-align: center; z-index: 10000; max-width: 80%;
                `;
                errorDiv.innerHTML = `
                    <h3 style="color: #ff5555; margin-bottom: 15px;">加载错误</h3>
                    <p style="margin-bottom: 20px;">无法加载3D渲染引擎，请刷新页面或检查网络连接</p>
                    <button onclick="location.reload()" style="
                        background: #3498db; color: white; border: none; padding: 10px 20px;
                        border-radius: 5px; cursor: pointer;
                    ">刷新页面</button>
                `;
                document.body.appendChild(errorDiv);
                
                return false;
            }
        }

        // 启动加载
        document.addEventListener('DOMContentLoaded', async () => {
            const threeLoaded = await loadThreeJS();
            if (!threeLoaded) {
                return;
            }
            
            // 加载完成
            document.getElementById('loading-bar').style.width = '100%';
            setTimeout(() => {
                document.getElementById('loading-progress').style.opacity = '0';
                setTimeout(() => {
                    document.getElementById('loading-progress').style.display = 'none';
                }, 300);
            }, 300);
        });
    </script>

    <!-- 添加控制台日志以便调试 -->
    <script>
        // 在页面加载完成后检查Three.js是否正确加载
        window.addEventListener('load', function() {
            console.log('Three.js库加载状态:', typeof THREE !== 'undefined' ? '成功' : '失败');

            if (typeof THREE === 'undefined') {
                console.error('Three.js库加载失败，请检查网络连接或刷新页面重试');
                return;
            }

            console.log('页面加载完成，Three.js可用');

            // 确保OrbitControls可用
            if (typeof THREE.OrbitControls === 'undefined') {
                console.warn('OrbitControls未加载，使用简化版替代');

                // 简化版的OrbitControls实现
                THREE.OrbitControls = function(camera, domElement) {
                    this.camera = camera;
                    this.domElement = domElement;
                    this.enableDamping = false;
                    this.dampingFactor = 0.25;
                    this.enableZoom = true;
                    this.autoRotate = false;
                    this.rotateSpeed = 1.0;
                    this.minDistance = 5;
                    this.maxDistance = 100;

                    this.update = function() {
                        // 简单实现，实际不做任何操作
                        return true;
                    };
                };
            }
        });
    </script>
</head>
<body>
    <!-- WebGL错误显示 -->
    <div id="webgl-error" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:#000; z-index:9999; color:white; padding:20px; text-align:center;">
        <h2>分子可视化程序初始化失败</h2>
        <p>可能原因：</p>
        <ul style="text-align:left; display:inline-block;">
            <li>浏览器不支持WebGL或相关功能被禁用</li>
            <li>显卡驱动程序需要更新</li>
            <li>系统资源不足</li>
        </ul>
        <p>解决方案：</p>
        <ol style="text-align:left; display:inline-block;">
            <li>使用最新版Chrome/Firefox浏览器</li>
            <li>检查浏览器设置中WebGL是否启用</li>
            <li>更新显卡驱动程序</li>
            <li>尝试在其他设备上打开</li>
        </ol>
        <p>错误详情: <span id="error-detail" style="color:#ff5555;"></span></p>
        <button onclick="location.reload()" style="padding:10px 20px; background:#3498db; color:white; border:none; border-radius:5px; cursor:pointer;">刷新页面</button>
    </div>

    <script>
        // 检查WebGL支持
        function checkWebGL() {
            try {
                const canvas = document.createElement('canvas');
                return !!window.WebGLRenderingContext && 
                      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
            } catch(e) {
                return false;
            }
        }

        // 页面加载时检查WebGL
        window.addEventListener('load', function() {
            if (!checkWebGL()) {
                const errorDiv = document.getElementById('webgl-error');
                errorDiv.style.display = 'block';
                document.getElementById('error-detail').textContent = 
                    '浏览器不支持WebGL或相关功能被禁用';
                
                // 隐藏加载进度条
                const loadingBar = document.getElementById('loading-progress');
                if (loadingBar) loadingBar.style.display = 'none';
            }
        });
    </script>
    <header>
        <h1>分子生物学动态可视化 - 增强版</h1>
        <p class="subtitle">更多分子种类 · 更精美的视觉效果 · 更丰富的动画</p>
    </header>

    <div class="container">
        <div class="control-panel">
            <h2>控制面板</h2>
            <div class="control-group">
                <label for="molecule-select">选择分子:</label>
                <select id="molecule-select">
                    <optgroup label="测试分子">
                        <option value="test">测试分子</option>
                    </optgroup>
                    <optgroup label="简单有机分子">
                        <option value="water">水分子</option>
                        <option value="methane">甲烷</option>
                    </optgroup>
                    <optgroup label="氨基酸">
                        <option value="tryptophan">色氨酸</option>
                        <option value="histidine">组氨酸</option>
                    </optgroup>
                    <optgroup label="核苷酸">
                        <option value="adenine">腺嘌呤</option>
                        <option value="thymine">胸腺嘧啶</option>
                    </optgroup>
                    <optgroup label="神经递质">
                        <option value="dopamine">多巴胺</option>
                    </optgroup>
                    <optgroup label="药物分子">
                        <option value="penicillinG">青霉素G</option>
                    </optgroup>
                    <optgroup label="科学研究实际模型 (PDB)">
                        <option value="pdb:6vxx">SARS-CoV-2刺突蛋白 (6VXX)</option>
                    </optgroup>
                </select>
            </div>

            <div class="control-group">
                <label for="pdb-id">加载PDB ID:</label>
                <div class="input-with-button">
                    <input type="text" id="pdb-id" placeholder="输入4位PDB ID..." maxlength="4">
                    <button id="load-pdb" class="secondary-button">加载</button>
                </div>
                <div class="help-text">从蛋白质数据库加载科学研究模型</div>
            </div>

            <div class="control-group">
                <label for="display-mode">显示模式:</label>
                <select id="display-mode">
                    <option value="ball-stick">球棍模型</option>
                    <option value="space-filling">空间填充模型</option>
                    <option value="cartoon">卡通模型</option>
                    <option value="wireframe">线框模型</option>
                </select>
            </div>

            <div class="control-group">
                <label for="animation-type">动画类型:</label>
                <select id="animation-type">
                    <option value="vibration">分子振动</option>
                    <option value="rotation">分子旋转</option>
                    <option value="molecularDynamics">分子动力学</option>
                    <option value="conformationChange">构象变化</option>
                    <option value="crystalVibration">晶体振动</option>
                    <option value="virusFloat">病毒漂浮</option>
                </select>
            </div>

            <div class="control-group">
                <label for="animation-speed">动画速度:</label>
                <input type="range" id="animation-speed" min="0" max="100" value="50">
            </div>

            <div class="control-group">
                <div class="button-row">
                    <button id="play-pause" class="primary-button">播放/暂停</button>
                    <button id="reset" class="secondary-button">重置视图</button>
                </div>
            </div>

            <div class="control-group">
                <label>渲染质量:</label>
                <div class="toggle-container">
                    <span>标准</span>
                    <label class="switch">
                        <input type="checkbox" id="quality-toggle" checked>
                        <span class="slider round"></span>
                    </label>
                    <span>高质量</span>
                </div>
            </div>

            <div class="control-group">
                <label>后期处理效果:</label>
                <div class="toggle-container">
                    <span>关闭</span>
                    <label class="switch">
                        <input type="checkbox" id="effects-toggle" checked>
                        <span class="slider round"></span>
                    </label>
                    <span>开启</span>
                </div>
            </div>

            <div class="info-panel">
                <h3>分子信息</h3>
                <div id="molecule-info">
                    选择一个分子以查看详细信息...
                </div>
            </div>
        </div>

        <div class="viewer-container" style="flex-grow: 1; position: relative; background-color: #000000; height: 100%;">
            <div id="molecule-viewer"></div>
        </div>
    </div>

    <footer>
        <p>分子生物学可视化增强版 &copy; 2023-2024</p>
        <div class="footer-links">
            <a href="#" id="about-link">关于</a>
            <a href="#" id="help-link">帮助</a>
        </div>
    </footer>

    <!-- 关于对话框 -->
    <div id="about-dialog" class="dialog">
        <div class="dialog-content">
            <span class="close-button">&times;</span>
            <h2>关于分子生物学可视化增强版</h2>
            <p>本程序是一个交互式的分子生物学可视化工具，使用Three.js实现3D渲染。</p>
            <p>增强版特点：</p>
            <ul>
                <li>支持更多种类的分子结构：
                    <ul>
                        <li>简单有机分子（甲烷、乙醇、二氧化碳等）</li>
                        <li>氨基酸（甘氨酸、丙氨酸、色氨酸、组氨酸等）</li>
                        <li>核苷酸（腺嘌呤、胸腺嘧啶等）</li>
                        <li>神经递质（多巴胺）</li>
                        <li>脂质（胆固醇、磷脂等）</li>
                        <li>碳水化合物（葡萄糖等）</li>
                        <li>能量分子（ATP等）</li>
                        <li>药物分子（阿司匹林、青霉素G等）</li>
                        <li>无机晶体结构（氯化钠）</li>
                        <li>病毒结构（冠状病毒、噬菌体）</li>
                    </ul>
                </li>
                <li><strong>基于科学研究的实际3D模型：</strong>
                    <ul>
                        <li>支持从蛋白质数据库(PDB)加载真实分子结构</li>
                        <li>包含来自X射线晶体学和冷冻电镜的精确原子坐标</li>
                        <li>可视化真实的生物分子如蛋白质、DNA和RNA</li>
                    </ul>
                </li>
                <li>高质量渲染和后期处理效果：
                    <ul>
                        <li>更真实的光照和材质</li>
                        <li>粒子效果和发光效果</li>
                    </ul>
                </li>
                <li>更丰富的动画效果：
                    <ul>
                        <li>分子动力学模拟 - 模拟分子在溶液中的布朗运动和内部振动</li>
                        <li>构象变化动画 - 模拟分子折叠、扭曲和展开等构象变化</li>
                        <li>晶体振动 - 模拟晶体结构中原子的热振动</li>
                        <li>病毒漂浮 - 模拟病毒颗粒在液体中的运动</li>
                        <li>噬菌体攻击 - 模拟噬菌体攻击细菌的过程</li>
                        <li>酶催化反应 - 模拟酶与底物相互作用的过程</li>
                        <li>更自然的分子振动 - 基于物理学原理的振动模式</li>
                    </ul>
                </li>
                <li>更直观的用户界面</li>
            </ul>

            <h3>关于蛋白质数据库(PDB)</h3>
            <p>蛋白质数据库(Protein Data Bank, PDB)是一个包含蛋白质、核酸等生物大分子三维结构数据的数据库。这些结构是通过X射线晶体学、核磁共振(NMR)和冷冻电镜等实验方法确定的，代表了科学研究中最精确的分子结构数据。</p>
            <p>本程序允许您通过输入4位PDB ID直接加载和可视化这些科学研究模型，让您能够探索真实的生物分子结构。</p>
            <p>访问<a href="https://www.rcsb.org" target="_blank">RCSB PDB官方网站</a>了解更多信息。</p>
        </div>
    </div>

    <!-- 帮助对话框 -->
    <div id="help-dialog" class="dialog">
        <div class="dialog-content">
            <span class="close-button">&times;</span>
            <h2>使用帮助</h2>
            <h3>基本操作：</h3>
            <ul>
                <li><strong>旋转视图</strong>：点击并拖动</li>
                <li><strong>缩放</strong>：滚动鼠标滚轮</li>
                <li><strong>平移</strong>：按住Shift键并拖动</li>
            </ul>
            <h3>控制面板：</h3>
            <ul>
                <li><strong>选择分子</strong>：从下拉菜单中选择不同的分子结构</li>
                <li><strong>加载PDB ID</strong>：输入4位PDB ID并点击"加载"按钮，从蛋白质数据库加载真实分子结构</li>
                <li><strong>显示模式</strong>：切换不同的分子表示方式</li>
                <li><strong>动画速度</strong>：调整分子动画的速度</li>
                <li><strong>渲染质量</strong>：切换标准/高质量渲染模式</li>
                <li><strong>后期处理</strong>：开启/关闭视觉效果</li>
            </ul>

            <h3>PDB加载说明：</h3>
            <ul>
                <li>您可以通过两种方式加载PDB分子：
                    <ul>
                        <li>从"科学研究实际模型 (PDB)"分类中选择预设的PDB分子</li>
                        <li>在"加载PDB ID"输入框中输入任意有效的4位PDB ID</li>
                    </ul>
                </li>
                <li>PDB ID是蛋白质数据库中每个分子结构的唯一标识符，通常由4个字母或数字组成</li>
                <li>示例PDB ID：1BNA (DNA双螺旋)、4HHB (血红蛋白)、6VXX (SARS-CoV-2刺突蛋白)</li>
                <li>您可以在<a href="https://www.rcsb.org" target="_blank">RCSB PDB网站</a>上搜索更多PDB ID</li>
            </ul>
        </div>
    </div>

    <!-- 调试脚本 -->
    <script>
        // 页面加载后检查MOLECULES对象
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                if (typeof MOLECULES !== 'undefined') {
                    console.log("MOLECULES对象已加载，可用分子:", Object.keys(MOLECULES));
                } else {
                    console.error("MOLECULES对象未加载，请检查js/molecules.js文件是否正确引入");
                }
            }, 500);
        });
    </script>

    <!-- 脚本加载错误处理 -->
    <script>
        // 添加脚本加载错误处理
        function loadScript(src, onError) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`脚本加载成功: ${src}`);
                    resolve();
                };
                script.onerror = (e) => {
                    console.error(`脚本加载失败: ${src}`, e);
                    if (onError) onError(e);
                    reject(e);
                };
                document.head.appendChild(script);
            });
        }

        // 按顺序加载脚本
        async function loadScripts() {
            try {
                await loadScript('js/FallbackRenderer.js');
                await loadScript('js/molecules.js');
                await loadScript('js/MoleculeLoader.js');
                await loadScript('js/MoleculeViewer.js');
                await loadScript('js/controls.js');
                await loadScript('js/main.js');
                console.log('所有脚本加载完成');
            } catch (error) {
                console.error('脚本加载过程中发生错误:', error);
                // 显示错误信息
                const viewerContainer = document.getElementById('molecule-viewer');
                if (viewerContainer) {
                    viewerContainer.innerHTML = `
                        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                                    display: flex; flex-direction: column; align-items: center; justify-content: center;
                                    background-color: rgba(0,0,0,0.8); color: white; text-align: center; padding: 20px;">
                            <h3 style="color: #ff5555; margin-bottom: 15px;">脚本加载错误</h3>
                            <p style="margin-bottom: 20px;">无法加载必要的JavaScript文件，请检查网络连接或刷新页面</p>
                            <button onclick="window.location.reload()"
                                    style="padding: 10px 20px; background-color: #3498db; border: none;
                                           border-radius: 5px; color: white; cursor: pointer; margin: 5px;">
                                刷新页面
                            </button>
                        </div>
                    `;
                }
            }
        }

        // 开始加载脚本
        loadScripts();
    </script>

    <!-- 调试信息 -->
    <script>
        // 在页面加载完成后检查渲染状态
        window.addEventListener('load', function() {
            // 检查MOLECULES对象是否正确加载
            console.log('MOLECULES对象状态:', typeof MOLECULES !== 'undefined' ? '已定义' : '未定义');
            if (typeof MOLECULES !== 'undefined') {
                console.log('MOLECULES对象键数量:', Object.keys(MOLECULES).length);
                console.log('MOLECULES对象所有键:', Object.keys(MOLECULES).join(', '));

                // 检查新增分子是否存在
                const newMolecules = ["tryptophan", "histidine", "adenine", "thymine", "dopamine", "cholesterol", "phospholipid", "glucose", "penicillinG"];
                newMolecules.forEach(function(moleculeName) {
                    console.log(`分子 ${moleculeName} 是否存在:`, MOLECULES.hasOwnProperty(moleculeName));
                });

                // 检查下拉菜单中的选项
                const selectElement = document.getElementById('molecule-select');
                if (selectElement) {
                    console.log('下拉菜单选项数量:', selectElement.options.length);

                    // 检查新增分子是否在下拉菜单中
                    let optionsFound = 0;
                    for (let i = 0; i < selectElement.options.length; i++) {
                        if (newMolecules.includes(selectElement.options[i].value)) {
                            optionsFound++;
                            console.log(`找到选项: ${selectElement.options[i].value}`);
                        }
                    }
                    console.log(`在下拉菜单中找到 ${optionsFound}/${newMolecules.length} 个新增分子`);
                }
            }

            setTimeout(function() {
                const viewerContainer = document.getElementById('molecule-viewer');
                if (viewerContainer && viewerContainer.children.length === 0) {
                    console.error('渲染容器为空，可能渲染失败');
                    alert('渲染初始化可能失败，请检查控制台错误信息或刷新页面');
                }
            }, 2000); // 给渲染器2秒钟初始化时间
        });
    </script>
</body>
</html>
